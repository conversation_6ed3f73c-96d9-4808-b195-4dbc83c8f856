<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="The Very High Level Layer" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/veryhigh.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The functions in this chapter will let you execute Python source code given in a file or a buffer, but they will not let you interact in a more detailed way with the interpreter. Several of these f..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The functions in this chapter will let you execute Python source code given in a file or a buffer, but they will not let you interact in a more detailed way with the interpreter. Several of these f..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>The Very High Level Layer &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Reference Counting" href="refcounting.html" />
    <link rel="prev" title="C API Stability" href="stable.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/veryhigh.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stable.html"
                          title="previous chapter">C API Stability</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="refcounting.html"
                          title="next chapter">Reference Counting</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/veryhigh.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="refcounting.html" title="Reference Counting"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="stable.html" title="C API Stability"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">The Very High Level Layer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="the-very-high-level-layer">
<span id="veryhigh"></span><h1>The Very High Level Layer<a class="headerlink" href="#the-very-high-level-layer" title="Link to this heading">¶</a></h1>
<p>The functions in this chapter will let you execute Python source code given in a
file or a buffer, but they will not let you interact in a more detailed way with
the interpreter.</p>
<p>Several of these functions accept a start symbol from the grammar as a
parameter.  The available start symbols are <a class="reference internal" href="#c.Py_eval_input" title="Py_eval_input"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_eval_input</span></code></a>,
<a class="reference internal" href="#c.Py_file_input" title="Py_file_input"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_file_input</span></code></a>, and <a class="reference internal" href="#c.Py_single_input" title="Py_single_input"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_single_input</span></code></a>.  These are described
following the functions which accept them as parameters.</p>
<p>Note also that several of these functions take <span class="c-expr sig sig-inline c"><span class="n">FILE</span><span class="p">*</span></span> parameters.  One
particular issue which needs to be handled carefully is that the <code class="xref c c-type docutils literal notranslate"><span class="pre">FILE</span></code>
structure for different C libraries can be different and incompatible.  Under
Windows (at least), it is possible for dynamically linked extensions to actually
use different libraries, so care should be taken that <span class="c-expr sig sig-inline c"><span class="n">FILE</span><span class="p">*</span></span> parameters
are only passed to these functions if it is certain that they were created by
the same library that the Python runtime is using.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_Main">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_Main</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_Main" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>The main program for the standard interpreter.  This is made available for
programs which embed Python.  The <em>argc</em> and <em>argv</em> parameters should be
prepared exactly as those which are passed to a C program’s <code class="xref c c-func docutils literal notranslate"><span class="pre">main()</span></code>
function (converted to wchar_t according to the user’s locale).  It is
important to note that the argument list may be modified (but the contents of
the strings pointed to by the argument list are not). The return value will
be <code class="docutils literal notranslate"><span class="pre">0</span></code> if the interpreter exits normally (i.e., without an exception),
<code class="docutils literal notranslate"><span class="pre">1</span></code> if the interpreter exits due to an exception, or <code class="docutils literal notranslate"><span class="pre">2</span></code> if the parameter
list does not represent a valid Python command line.</p>
<p>Note that if an otherwise unhandled <a class="reference internal" href="../library/exceptions.html#SystemExit" title="SystemExit"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemExit</span></code></a> is raised, this
function will not return <code class="docutils literal notranslate"><span class="pre">1</span></code>, but exit the process, as long as
<a class="reference internal" href="init_config.html#c.PyConfig.inspect" title="PyConfig.inspect"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.inspect</span></code></a> is zero.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_BytesMain">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_BytesMain</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_BytesMain" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.8.</em><p>Similar to <a class="reference internal" href="#c.Py_Main" title="Py_Main"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Main()</span></code></a> but <em>argv</em> is an array of bytes strings.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_AnyFile">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_AnyFile</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_AnyFile" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_AnyFileExFlags" title="PyRun_AnyFileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_AnyFileExFlags()</span></code></a> below, leaving
<em>closeit</em> set to <code class="docutils literal notranslate"><span class="pre">0</span></code> and <em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_AnyFileFlags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_AnyFileFlags</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_AnyFileFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_AnyFileExFlags" title="PyRun_AnyFileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_AnyFileExFlags()</span></code></a> below, leaving
the <em>closeit</em> argument set to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_AnyFileEx">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_AnyFileEx</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">closeit</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_AnyFileEx" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_AnyFileExFlags" title="PyRun_AnyFileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_AnyFileExFlags()</span></code></a> below, leaving
the <em>flags</em> argument set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_AnyFileExFlags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_AnyFileExFlags</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">closeit</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_AnyFileExFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>If <em>fp</em> refers to a file associated with an interactive device (console or
terminal input or Unix pseudo-terminal), return the value of
<a class="reference internal" href="#c.PyRun_InteractiveLoop" title="PyRun_InteractiveLoop"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_InteractiveLoop()</span></code></a>, otherwise return the result of
<a class="reference internal" href="#c.PyRun_SimpleFile" title="PyRun_SimpleFile"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_SimpleFile()</span></code></a>.  <em>filename</em> is decoded from the filesystem
encoding (<a class="reference internal" href="../library/sys.html#sys.getfilesystemencoding" title="sys.getfilesystemencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getfilesystemencoding()</span></code></a>).  If <em>filename</em> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, this
function uses <code class="docutils literal notranslate"><span class="pre">&quot;???&quot;</span></code> as the filename.
If <em>closeit</em> is true, the file is closed before
<code class="docutils literal notranslate"><span class="pre">PyRun_SimpleFileExFlags()</span></code> returns.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_SimpleString">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_SimpleString</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">command</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_SimpleString" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_SimpleStringFlags" title="PyRun_SimpleStringFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_SimpleStringFlags()</span></code></a> below,
leaving the <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><code class="xref c c-struct docutils literal notranslate"><span class="pre">PyCompilerFlags</span></code></a>* argument set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_SimpleStringFlags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_SimpleStringFlags</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">command</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_SimpleStringFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Executes the Python source code from <em>command</em> in the <a class="reference internal" href="../library/__main__.html#module-__main__" title="__main__: The environment where top-level code is run. Covers command-line interfaces, import-time behavior, and ``__name__ == '__main__'``."><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code></a> module
according to the <em>flags</em> argument. If <a class="reference internal" href="../library/__main__.html#module-__main__" title="__main__: The environment where top-level code is run. Covers command-line interfaces, import-time behavior, and ``__name__ == '__main__'``."><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code></a> does not already exist, it
is created.  Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> on success or <code class="docutils literal notranslate"><span class="pre">-1</span></code> if an exception was raised.  If
there was an error, there is no way to get the exception information. For the
meaning of <em>flags</em>, see below.</p>
<p>Note that if an otherwise unhandled <a class="reference internal" href="../library/exceptions.html#SystemExit" title="SystemExit"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemExit</span></code></a> is raised, this
function will not return <code class="docutils literal notranslate"><span class="pre">-1</span></code>, but exit the process, as long as
<a class="reference internal" href="init_config.html#c.PyConfig.inspect" title="PyConfig.inspect"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.inspect</span></code></a> is zero.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_SimpleFile">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_SimpleFile</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_SimpleFile" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_SimpleFileExFlags" title="PyRun_SimpleFileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_SimpleFileExFlags()</span></code></a> below,
leaving <em>closeit</em> set to <code class="docutils literal notranslate"><span class="pre">0</span></code> and <em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_SimpleFileEx">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_SimpleFileEx</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">closeit</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_SimpleFileEx" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_SimpleFileExFlags" title="PyRun_SimpleFileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_SimpleFileExFlags()</span></code></a> below,
leaving <em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_SimpleFileExFlags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_SimpleFileExFlags</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">closeit</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_SimpleFileExFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Similar to <a class="reference internal" href="#c.PyRun_SimpleStringFlags" title="PyRun_SimpleStringFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_SimpleStringFlags()</span></code></a>, but the Python source code is read
from <em>fp</em> instead of an in-memory string. <em>filename</em> should be the name of
the file, it is decoded from <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>.
If <em>closeit</em> is true, the file is closed before
<code class="docutils literal notranslate"><span class="pre">PyRun_SimpleFileExFlags()</span></code> returns.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Windows, <em>fp</em> should be opened as binary mode (e.g. <code class="docutils literal notranslate"><span class="pre">fopen(filename,</span> <span class="pre">&quot;rb&quot;)</span></code>).
Otherwise, Python may not handle script file with LF line ending correctly.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_InteractiveOne">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_InteractiveOne</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_InteractiveOne" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_InteractiveOneFlags" title="PyRun_InteractiveOneFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_InteractiveOneFlags()</span></code></a> below,
leaving <em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_InteractiveOneFlags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_InteractiveOneFlags</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_InteractiveOneFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Read and execute a single statement from a file associated with an
interactive device according to the <em>flags</em> argument.  The user will be
prompted using <code class="docutils literal notranslate"><span class="pre">sys.ps1</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.ps2</span></code>.  <em>filename</em> is decoded from the
<a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>.</p>
<p>Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> when the input was
executed successfully, <code class="docutils literal notranslate"><span class="pre">-1</span></code> if there was an exception, or an error code
from the <code class="file docutils literal notranslate"><span class="pre">errcode.h</span></code> include file distributed as part of Python if
there was a parse error.  (Note that <code class="file docutils literal notranslate"><span class="pre">errcode.h</span></code> is not included by
<code class="file docutils literal notranslate"><span class="pre">Python.h</span></code>, so must be included specifically if needed.)</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_InteractiveLoop">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_InteractiveLoop</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_InteractiveLoop" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_InteractiveLoopFlags" title="PyRun_InteractiveLoopFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_InteractiveLoopFlags()</span></code></a> below,
leaving <em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_InteractiveLoopFlags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_InteractiveLoopFlags</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_InteractiveLoopFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Read and execute statements from a file associated with an interactive device
until EOF is reached.  The user will be prompted using <code class="docutils literal notranslate"><span class="pre">sys.ps1</span></code> and
<code class="docutils literal notranslate"><span class="pre">sys.ps2</span></code>.  <em>filename</em> is decoded from the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and
error handler</span></a>.  Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> at EOF or a negative number upon failure.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyOS_InputHook">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_InputHook</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><span class="kt"><span class="pre">void</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.PyOS_InputHook" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Can be set to point to a function with the prototype
<code class="docutils literal notranslate"><span class="pre">int</span> <span class="pre">func(void)</span></code>.  The function will be called when Python’s
interpreter prompt is about to become idle and wait for user input
from the terminal.  The return value is ignored.  Overriding this
hook can be used to integrate the interpreter’s prompt with other
event loops, as done in the <code class="file docutils literal notranslate"><span class="pre">Modules/_tkinter.c</span></code> in the
Python source code.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>This function is only called from the
<a class="reference internal" href="init.html#sub-interpreter-support"><span class="std std-ref">main interpreter</span></a>.</p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyOS_ReadlineFunctionPointer">
<span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_ReadlineFunctionPointer</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><span class="n"><span class="pre">FILE</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="n"><span class="pre">FILE</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.PyOS_ReadlineFunctionPointer" title="Link to this definition">¶</a><br /></dt>
<dd><p>Can be set to point to a function with the prototype
<code class="docutils literal notranslate"><span class="pre">char</span> <span class="pre">*func(FILE</span> <span class="pre">*stdin,</span> <span class="pre">FILE</span> <span class="pre">*stdout,</span> <span class="pre">char</span> <span class="pre">*prompt)</span></code>,
overriding the default function used to read a single line of input
at the interpreter’s prompt.  The function is expected to output
the string <em>prompt</em> if it’s not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, and then read a line of
input from the provided standard input file, returning the
resulting string.  For example, The <a class="reference internal" href="../library/readline.html#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> module sets
this hook to provide line-editing and tab-completion features.</p>
<p>The result must be a string allocated by <a class="reference internal" href="memory.html#c.PyMem_RawMalloc" title="PyMem_RawMalloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawMalloc()</span></code></a> or
<a class="reference internal" href="memory.html#c.PyMem_RawRealloc" title="PyMem_RawRealloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawRealloc()</span></code></a>, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an error occurred.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The result must be allocated by <a class="reference internal" href="memory.html#c.PyMem_RawMalloc" title="PyMem_RawMalloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawMalloc()</span></code></a> or
<a class="reference internal" href="memory.html#c.PyMem_RawRealloc" title="PyMem_RawRealloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawRealloc()</span></code></a>, instead of being allocated by
<a class="reference internal" href="memory.html#c.PyMem_Malloc" title="PyMem_Malloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_Malloc()</span></code></a> or <a class="reference internal" href="memory.html#c.PyMem_Realloc" title="PyMem_Realloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_Realloc()</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>This function is only called from the
<a class="reference internal" href="init.html#sub-interpreter-support"><span class="std std-ref">main interpreter</span></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_String">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_String</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_String" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_StringFlags" title="PyRun_StringFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_StringFlags()</span></code></a> below, leaving
<em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_StringFlags">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_StringFlags</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_StringFlags" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>Execute Python source code from <em>str</em> in the context specified by the
objects <em>globals</em> and <em>locals</em> with the compiler flags specified by
<em>flags</em>.  <em>globals</em> must be a dictionary; <em>locals</em> can be any object
that implements the mapping protocol.  The parameter <em>start</em> specifies
the start token that should be used to parse the source code.</p>
<p>Returns the result of executing the code as a Python object, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an
exception was raised.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_File">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_File</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_File" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_FileExFlags" title="PyRun_FileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_FileExFlags()</span></code></a> below, leaving
<em>closeit</em> set to <code class="docutils literal notranslate"><span class="pre">0</span></code> and <em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_FileEx">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_FileEx</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">closeit</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_FileEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_FileExFlags" title="PyRun_FileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_FileExFlags()</span></code></a> below, leaving
<em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_FileFlags">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_FileFlags</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_FileFlags" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>This is a simplified interface to <a class="reference internal" href="#c.PyRun_FileExFlags" title="PyRun_FileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_FileExFlags()</span></code></a> below, leaving
<em>closeit</em> set to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyRun_FileExFlags">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyRun_FileExFlags</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">FILE</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">fp</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">closeit</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyRun_FileExFlags" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>Similar to <a class="reference internal" href="#c.PyRun_StringFlags" title="PyRun_StringFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_StringFlags()</span></code></a>, but the Python source code is read from
<em>fp</em> instead of an in-memory string. <em>filename</em> should be the name of the file,
it is decoded from the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>.
If <em>closeit</em> is true, the file is closed before <a class="reference internal" href="#c.PyRun_FileExFlags" title="PyRun_FileExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyRun_FileExFlags()</span></code></a>
returns.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_CompileString">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_CompileString</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_CompileString" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This is a simplified interface to <a class="reference internal" href="#c.Py_CompileStringFlags" title="Py_CompileStringFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CompileStringFlags()</span></code></a> below, leaving
<em>flags</em> set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_CompileStringFlags">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_CompileStringFlags</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_CompileStringFlags" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>This is a simplified interface to <a class="reference internal" href="#c.Py_CompileStringExFlags" title="Py_CompileStringExFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CompileStringExFlags()</span></code></a> below, with
<em>optimize</em> set to <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_CompileStringObject">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_CompileStringObject</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">optimize</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_CompileStringObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>Parse and compile the Python source code in <em>str</em>, returning the resulting code
object.  The start token is given by <em>start</em>; this can be used to constrain the
code which can be compiled and should be <a class="reference internal" href="#c.Py_eval_input" title="Py_eval_input"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_eval_input</span></code></a>,
<a class="reference internal" href="#c.Py_file_input" title="Py_file_input"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_file_input</span></code></a>, or <a class="reference internal" href="#c.Py_single_input" title="Py_single_input"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_single_input</span></code></a>.  The filename specified by
<em>filename</em> is used to construct the code object and may appear in tracebacks or
<a class="reference internal" href="../library/exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a> exception messages.  This returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the code
cannot be parsed or compiled.</p>
<p>The integer <em>optimize</em> specifies the optimization level of the compiler; a
value of <code class="docutils literal notranslate"><span class="pre">-1</span></code> selects the optimization level of the interpreter as given by
<a class="reference internal" href="../using/cmdline.html#cmdoption-O"><code class="xref std std-option docutils literal notranslate"><span class="pre">-O</span></code></a> options.  Explicit levels are <code class="docutils literal notranslate"><span class="pre">0</span></code> (no optimization;
<code class="docutils literal notranslate"><span class="pre">__debug__</span></code> is true), <code class="docutils literal notranslate"><span class="pre">1</span></code> (asserts are removed, <code class="docutils literal notranslate"><span class="pre">__debug__</span></code> is false)
or <code class="docutils literal notranslate"><span class="pre">2</span></code> (docstrings are removed too).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_CompileStringExFlags">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_CompileStringExFlags</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">filename</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">start</span></span>, <a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">flags</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">optimize</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_CompileStringExFlags" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>Like <a class="reference internal" href="#c.Py_CompileStringObject" title="Py_CompileStringObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CompileStringObject()</span></code></a>, but <em>filename</em> is a byte string
decoded from the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_EvalCode">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_EvalCode</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">co</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_EvalCode" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This is a simplified interface to <a class="reference internal" href="#c.PyEval_EvalCodeEx" title="PyEval_EvalCodeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_EvalCodeEx()</span></code></a>, with just
the code object, and global and local variables.  The other arguments are
set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_EvalCodeEx">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_EvalCodeEx</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">co</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">globals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">locals</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argcount</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kws</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">kwcount</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">defs</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">defcount</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">kwdefs</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">closure</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_EvalCodeEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Evaluate a precompiled code object, given a particular environment for its
evaluation.  This environment consists of a dictionary of global variables,
a mapping object of local variables, arrays of arguments, keywords and
defaults, a dictionary of default values for <a class="reference internal" href="../glossary.html#keyword-only-parameter"><span class="std std-ref">keyword-only</span></a> arguments and a closure tuple of cells.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_EvalFrame">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_EvalFrame</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="frame.html#c.PyFrameObject" title="PyFrameObject"><span class="n"><span class="pre">PyFrameObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">f</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_EvalFrame" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Evaluate an execution frame.  This is a simplified interface to
<a class="reference internal" href="#c.PyEval_EvalFrameEx" title="PyEval_EvalFrameEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_EvalFrameEx()</span></code></a>, for backward compatibility.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_EvalFrameEx">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_EvalFrameEx</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="frame.html#c.PyFrameObject" title="PyFrameObject"><span class="n"><span class="pre">PyFrameObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">f</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">throwflag</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_EvalFrameEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This is the main, unvarnished function of Python interpretation.  The code
object associated with the execution frame <em>f</em> is executed, interpreting
bytecode and executing calls as needed.  The additional <em>throwflag</em>
parameter can mostly be ignored - if true, then it causes an exception
to immediately be thrown; this is used for the <a class="reference internal" href="../reference/expressions.html#generator.throw" title="generator.throw"><code class="xref py py-meth docutils literal notranslate"><span class="pre">throw()</span></code></a>
methods of generator objects.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>This function now includes a debug assertion to help ensure that it
does not silently discard an active exception.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_MergeCompilerFlags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_MergeCompilerFlags</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyCompilerFlags" title="PyCompilerFlags"><span class="n"><span class="pre">PyCompilerFlags</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">cf</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_MergeCompilerFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>This function changes the flags of the current evaluation frame, and returns
true on success, false on failure.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_eval_input">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_eval_input</span></span></span><a class="headerlink" href="#c.Py_eval_input" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-0">The start symbol from the Python grammar for isolated expressions; for use with
<a class="reference internal" href="#c.Py_CompileString" title="Py_CompileString"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CompileString()</span></code></a>.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_file_input">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_file_input</span></span></span><a class="headerlink" href="#c.Py_file_input" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-1">The start symbol from the Python grammar for sequences of statements as read
from a file or other source; for use with <a class="reference internal" href="#c.Py_CompileString" title="Py_CompileString"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CompileString()</span></code></a>.  This is
the symbol to use when compiling arbitrarily long Python source code.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_single_input">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_single_input</span></span></span><a class="headerlink" href="#c.Py_single_input" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-2">The start symbol from the Python grammar for a single statement; for use with
<a class="reference internal" href="#c.Py_CompileString" title="Py_CompileString"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CompileString()</span></code></a>. This is the symbol used for the interactive
interpreter loop.</p>
</dd></dl>

<dl class="c struct">
<dt class="sig sig-object c" id="c.PyCompilerFlags">
<span class="k"><span class="pre">struct</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyCompilerFlags</span></span></span><a class="headerlink" href="#c.PyCompilerFlags" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is the structure used to hold compiler flags.  In cases where code is only
being compiled, it is passed as <code class="docutils literal notranslate"><span class="pre">int</span> <span class="pre">flags</span></code>, and in cases where code is being
executed, it is passed as <code class="docutils literal notranslate"><span class="pre">PyCompilerFlags</span> <span class="pre">*flags</span></code>.  In this case, <code class="docutils literal notranslate"><span class="pre">from</span>
<span class="pre">__future__</span> <span class="pre">import</span></code> can modify <em>flags</em>.</p>
<p>Whenever <code class="docutils literal notranslate"><span class="pre">PyCompilerFlags</span> <span class="pre">*flags</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, <a class="reference internal" href="#c.PyCompilerFlags.cf_flags" title="PyCompilerFlags.cf_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">cf_flags</span></code></a> is treated as
equal to <code class="docutils literal notranslate"><span class="pre">0</span></code>, and any modification due to <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span></code> is
discarded.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyCompilerFlags.cf_flags">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">cf_flags</span></span></span><a class="headerlink" href="#c.PyCompilerFlags.cf_flags" title="Link to this definition">¶</a><br /></dt>
<dd><p>Compiler flags.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyCompilerFlags.cf_feature_version">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">cf_feature_version</span></span></span><a class="headerlink" href="#c.PyCompilerFlags.cf_feature_version" title="Link to this definition">¶</a><br /></dt>
<dd><p><em>cf_feature_version</em> is the minor Python version. It should be
initialized to <code class="docutils literal notranslate"><span class="pre">PY_MINOR_VERSION</span></code>.</p>
<p>The field is ignored by default, it is used if and only if
<code class="docutils literal notranslate"><span class="pre">PyCF_ONLY_AST</span></code> flag is set in <a class="reference internal" href="#c.PyCompilerFlags.cf_flags" title="PyCompilerFlags.cf_flags"><code class="xref c c-member docutils literal notranslate"><span class="pre">cf_flags</span></code></a>.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added <em>cf_feature_version</em> field.</p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.CO_FUTURE_DIVISION">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">CO_FUTURE_DIVISION</span></span></span><a class="headerlink" href="#c.CO_FUTURE_DIVISION" title="Link to this definition">¶</a><br /></dt>
<dd><p>This bit can be set in <em>flags</em> to cause division operator <code class="docutils literal notranslate"><span class="pre">/</span></code> to be
interpreted as “true division” according to <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0238/"><strong>PEP 238</strong></a>.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="stable.html"
                          title="previous chapter">C API Stability</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="refcounting.html"
                          title="next chapter">Reference Counting</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/veryhigh.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="refcounting.html" title="Reference Counting"
             >next</a> |</li>
        <li class="right" >
          <a href="stable.html" title="C API Stability"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">The Very High Level Layer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>