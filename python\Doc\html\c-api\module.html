<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Module Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/module.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Initializing C modules: Modules objects are usually created from extension modules (shared libraries which export an initialization function), or compiled-in modules (where the initialization funct..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Initializing C modules: Modules objects are usually created from extension modules (shared libraries which export an initialization function), or compiled-in modules (where the initialization funct..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Module Objects &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Iterator Objects" href="iterator.html" />
    <link rel="prev" title="File Objects" href="file.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/module.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Module Objects</a><ul>
<li><a class="reference internal" href="#initializing-c-modules">Initializing C modules</a><ul>
<li><a class="reference internal" href="#single-phase-initialization">Single-phase initialization</a></li>
<li><a class="reference internal" href="#multi-phase-initialization">Multi-phase initialization</a></li>
<li><a class="reference internal" href="#low-level-module-creation-functions">Low-level module creation functions</a></li>
<li><a class="reference internal" href="#support-functions">Support functions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-lookup">Module lookup</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="file.html"
                          title="previous chapter">File Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="iterator.html"
                          title="next chapter">Iterator Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/module.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="iterator.html" title="Iterator Objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="file.html" title="File Objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Module Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-objects">
<span id="moduleobjects"></span><h1>Module Objects<a class="headerlink" href="#module-objects" title="Link to this heading">¶</a></h1>
<dl class="c var" id="index-0">
<dt class="sig sig-object c" id="c.PyModule_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_Type</span></span></span><a class="headerlink" href="#c.PyModule_Type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-1">This instance of <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> represents the Python module type.  This
is exposed to Python programs as <code class="docutils literal notranslate"><span class="pre">types.ModuleType</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>p</em> is a module object, or a subtype of a module object.
This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_CheckExact">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_CheckExact</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_CheckExact" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if <em>p</em> is a module object, but not a subtype of
<a class="reference internal" href="#c.PyModule_Type" title="PyModule_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyModule_Type</span></code></a>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_NewObject">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_NewObject</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_NewObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p id="index-2">Return a new module object with the <a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> attribute set to <em>name</em>.
The module’s <a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">__doc__</span></code>, <a class="reference internal" href="../reference/import.html#package__" title="__package__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__package__</span></code></a>, and
<a class="reference internal" href="../reference/import.html#loader__" title="__loader__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__loader__</span></code></a> attributes are filled in (all but <a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> are set
to <code class="docutils literal notranslate"><span class="pre">None</span></code>); the caller is responsible for providing a <a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a>
attribute.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><a class="reference internal" href="../reference/import.html#package__" title="__package__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__package__</span></code></a> and <a class="reference internal" href="../reference/import.html#loader__" title="__loader__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__loader__</span></code></a> are set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_New">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_New</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Similar to <a class="reference internal" href="#c.PyModule_NewObject" title="PyModule_NewObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_NewObject()</span></code></a>, but the name is a UTF-8 encoded
string instead of a Unicode object.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_GetDict">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_GetDict</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_GetDict" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: Borrowed reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-3">Return the dictionary object that implements <em>module</em>’s namespace; this object
is the same as the <a class="reference internal" href="../library/stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attribute of the module object.
If <em>module</em> is not a module object (or a subtype of a module object),
<a class="reference internal" href="../library/exceptions.html#SystemError" title="SystemError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemError</span></code></a> is raised and <code class="docutils literal notranslate"><span class="pre">NULL</span></code> is returned.</p>
<p>It is recommended extensions use other <code class="docutils literal notranslate"><span class="pre">PyModule_*</span></code> and
<code class="docutils literal notranslate"><span class="pre">PyObject_*</span></code> functions rather than directly manipulate a module’s
<a class="reference internal" href="../library/stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_GetNameObject">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_GetNameObject</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_GetNameObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p id="index-4">Return <em>module</em>’s <a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> value.  If the module does not provide one,
or if it is not a string, <a class="reference internal" href="../library/exceptions.html#SystemError" title="SystemError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemError</span></code></a> is raised and <code class="docutils literal notranslate"><span class="pre">NULL</span></code> is returned.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_GetName">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_GetName</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_GetName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Similar to <a class="reference internal" href="#c.PyModule_GetNameObject" title="PyModule_GetNameObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetNameObject()</span></code></a> but return the name encoded to
<code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_GetState">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_GetState</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_GetState" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the “state” of the module, that is, a pointer to the block of memory
allocated at module creation time, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  See
<a class="reference internal" href="#c.PyModuleDef.m_size" title="PyModuleDef.m_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyModuleDef.m_size</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_GetDef">
<a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_GetDef</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_GetDef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return a pointer to the <a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyModuleDef</span></code></a> struct from which the module was
created, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the module wasn’t created from a definition.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_GetFilenameObject">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_GetFilenameObject</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_GetFilenameObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-5">Return the name of the file from which <em>module</em> was loaded using <em>module</em>’s
<a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a> attribute.  If this is not defined, or if it is not a
unicode string, raise <a class="reference internal" href="../library/exceptions.html#SystemError" title="SystemError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemError</span></code></a> and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>; otherwise return
a reference to a Unicode object.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_GetFilename">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_GetFilename</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_GetFilename" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Similar to <a class="reference internal" href="#c.PyModule_GetFilenameObject" title="PyModule_GetFilenameObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetFilenameObject()</span></code></a> but return the filename
encoded to ‘utf-8’.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.2: </span><a class="reference internal" href="#c.PyModule_GetFilename" title="PyModule_GetFilename"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetFilename()</span></code></a> raises <a class="reference internal" href="../library/exceptions.html#UnicodeEncodeError" title="UnicodeEncodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnicodeEncodeError</span></code></a> on
unencodable filenames, use <a class="reference internal" href="#c.PyModule_GetFilenameObject" title="PyModule_GetFilenameObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetFilenameObject()</span></code></a> instead.</p>
</div>
</dd></dl>

<section id="initializing-c-modules">
<span id="initializing-modules"></span><h2>Initializing C modules<a class="headerlink" href="#initializing-c-modules" title="Link to this heading">¶</a></h2>
<p>Modules objects are usually created from extension modules (shared libraries
which export an initialization function), or compiled-in modules
(where the initialization function is added using <a class="reference internal" href="import.html#c.PyImport_AppendInittab" title="PyImport_AppendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_AppendInittab()</span></code></a>).
See <a class="reference internal" href="../extending/building.html#building"><span class="std std-ref">Building C and C++ Extensions</span></a> or <a class="reference internal" href="../extending/embedding.html#extending-with-embedding"><span class="std std-ref">Extending Embedded Python</span></a> for details.</p>
<p>The initialization function can either pass a module definition instance
to <a class="reference internal" href="#c.PyModule_Create" title="PyModule_Create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_Create()</span></code></a>, and return the resulting module object,
or request “multi-phase initialization” by returning the definition struct itself.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyModuleDef">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModuleDef</span></span></span><a class="headerlink" href="#c.PyModuleDef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> (including all members).</em><p>The module definition struct, which holds all information needed to create
a module object. There is usually only one statically initialized variable
of this type for each module.</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_base">
<span class="n"><span class="pre">PyModuleDef_Base</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">m_base</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_base" title="Link to this definition">¶</a><br /></dt>
<dd><p>Always initialize this member to <code class="xref c c-macro docutils literal notranslate"><span class="pre">PyModuleDef_HEAD_INIT</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_name">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">m_name</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_name" title="Link to this definition">¶</a><br /></dt>
<dd><p>Name for the new module.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_doc">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">m_doc</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_doc" title="Link to this definition">¶</a><br /></dt>
<dd><p>Docstring for the module; usually a docstring variable created with
<a class="reference internal" href="intro.html#c.PyDoc_STRVAR" title="PyDoc_STRVAR"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyDoc_STRVAR</span></code></a> is used.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_size">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">m_size</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_size" title="Link to this definition">¶</a><br /></dt>
<dd><p>Module state may be kept in a per-module memory area that can be
retrieved with <a class="reference internal" href="#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a>, rather than in static globals.
This makes modules safe for use in multiple sub-interpreters.</p>
<p>This memory area is allocated based on <em>m_size</em> on module creation,
and freed when the module object is deallocated, after the
<a class="reference internal" href="#c.PyModuleDef.m_free" title="PyModuleDef.m_free"><code class="xref c c-member docutils literal notranslate"><span class="pre">m_free</span></code></a> function has been called, if present.</p>
<p>Setting <code class="docutils literal notranslate"><span class="pre">m_size</span></code> to <code class="docutils literal notranslate"><span class="pre">-1</span></code> means that the module does not support
sub-interpreters, because it has global state.</p>
<p>Setting it to a non-negative value means that the module can be
re-initialized and specifies the additional amount of memory it requires
for its state. Non-negative <code class="docutils literal notranslate"><span class="pre">m_size</span></code> is required for multi-phase
initialization.</p>
<p>See <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-3121/"><strong>PEP 3121</strong></a> for more details.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_methods">
<a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><span class="n"><span class="pre">PyMethodDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">m_methods</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_methods" title="Link to this definition">¶</a><br /></dt>
<dd><p>A pointer to a table of module-level functions, described by
<a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMethodDef</span></code></a> values.  Can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if no functions are present.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_slots">
<a class="reference internal" href="#c.PyModuleDef_Slot" title="PyModuleDef_Slot"><span class="n"><span class="pre">PyModuleDef_Slot</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">m_slots</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_slots" title="Link to this definition">¶</a><br /></dt>
<dd><p>An array of slot definitions for multi-phase initialization, terminated by
a <code class="docutils literal notranslate"><span class="pre">{0,</span> <span class="pre">NULL}</span></code> entry.
When using single-phase initialization, <em>m_slots</em> must be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Prior to version 3.5, this member was always set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,
and was defined as:</p>
<blockquote>
<div><dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_slots.m_reload">
<a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><span class="n"><span class="pre">inquiry</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">m_reload</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_slots.m_reload" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</div></blockquote>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_traverse">
<a class="reference internal" href="gcsupport.html#c.traverseproc" title="traverseproc"><span class="n"><span class="pre">traverseproc</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">m_traverse</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_traverse" title="Link to this definition">¶</a><br /></dt>
<dd><p>A traversal function to call during GC traversal of the module object, or
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> if not needed.</p>
<p>This function is not called if the module state was requested but is not
allocated yet. This is the case immediately after the module is created
and before the module is executed (<a class="reference internal" href="#c.Py_mod_exec" title="Py_mod_exec"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_mod_exec</span></code></a> function). More
precisely, this function is not called if <a class="reference internal" href="#c.PyModuleDef.m_size" title="PyModuleDef.m_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">m_size</span></code></a> is greater
than 0 and the module state (as returned by <a class="reference internal" href="#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a>)
is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>No longer called before the module state is allocated.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_clear">
<a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><span class="n"><span class="pre">inquiry</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">m_clear</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_clear" title="Link to this definition">¶</a><br /></dt>
<dd><p>A clear function to call during GC clearing of the module object, or
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> if not needed.</p>
<p>This function is not called if the module state was requested but is not
allocated yet. This is the case immediately after the module is created
and before the module is executed (<a class="reference internal" href="#c.Py_mod_exec" title="Py_mod_exec"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_mod_exec</span></code></a> function). More
precisely, this function is not called if <a class="reference internal" href="#c.PyModuleDef.m_size" title="PyModuleDef.m_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">m_size</span></code></a> is greater
than 0 and the module state (as returned by <a class="reference internal" href="#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a>)
is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Like <a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_clear" title="PyTypeObject.tp_clear"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyTypeObject.tp_clear</span></code></a>, this function is not <em>always</em>
called before a module is deallocated. For example, when reference
counting is enough to determine that an object is no longer used,
the cyclic garbage collector is not involved and
<a class="reference internal" href="#c.PyModuleDef.m_free" title="PyModuleDef.m_free"><code class="xref c c-member docutils literal notranslate"><span class="pre">m_free</span></code></a> is called directly.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>No longer called before the module state is allocated.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef.m_free">
<a class="reference internal" href="typeobj.html#c.freefunc" title="freefunc"><span class="n"><span class="pre">freefunc</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">m_free</span></span></span><a class="headerlink" href="#c.PyModuleDef.m_free" title="Link to this definition">¶</a><br /></dt>
<dd><p>A function to call during deallocation of the module object, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>
if not needed.</p>
<p>This function is not called if the module state was requested but is not
allocated yet. This is the case immediately after the module is created
and before the module is executed (<a class="reference internal" href="#c.Py_mod_exec" title="Py_mod_exec"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_mod_exec</span></code></a> function). More
precisely, this function is not called if <a class="reference internal" href="#c.PyModuleDef.m_size" title="PyModuleDef.m_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">m_size</span></code></a> is greater
than 0 and the module state (as returned by <a class="reference internal" href="#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a>)
is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>No longer called before the module state is allocated.</p>
</div>
</dd></dl>

</dd></dl>

<section id="single-phase-initialization">
<h3>Single-phase initialization<a class="headerlink" href="#single-phase-initialization" title="Link to this heading">¶</a></h3>
<p>The module initialization function may create and return the module object
directly. This is referred to as “single-phase initialization”, and uses one
of the following two module creation functions:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_Create">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_Create</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_Create" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>Create a new module object, given the definition in <em>def</em>.  This behaves
like <a class="reference internal" href="#c.PyModule_Create2" title="PyModule_Create2"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_Create2()</span></code></a> with <em>module_api_version</em> set to
<code class="xref c c-macro docutils literal notranslate"><span class="pre">PYTHON_API_VERSION</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_Create2">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_Create2</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">module_api_version</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_Create2" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Create a new module object, given the definition in <em>def</em>, assuming the
API version <em>module_api_version</em>.  If that version does not match the version
of the running interpreter, a <a class="reference internal" href="../library/exceptions.html#RuntimeWarning" title="RuntimeWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeWarning</span></code></a> is emitted.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Most uses of this function should be using <a class="reference internal" href="#c.PyModule_Create" title="PyModule_Create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_Create()</span></code></a>
instead; only use this if you are sure you need it.</p>
</div>
</dd></dl>

<p>Before it is returned from in the initialization function, the resulting module
object is typically populated using functions like <a class="reference internal" href="#c.PyModule_AddObjectRef" title="PyModule_AddObjectRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddObjectRef()</span></code></a>.</p>
</section>
<section id="multi-phase-initialization">
<span id="id1"></span><h3>Multi-phase initialization<a class="headerlink" href="#multi-phase-initialization" title="Link to this heading">¶</a></h3>
<p>An alternate way to specify extensions is to request “multi-phase initialization”.
Extension modules created this way behave more like Python modules: the
initialization is split between the <em>creation phase</em>, when the module object
is created, and the <em>execution phase</em>, when it is populated.
The distinction is similar to the <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> methods
of classes.</p>
<p>Unlike modules created using single-phase initialization, these modules are not
singletons: if the <em>sys.modules</em> entry is removed and the module is re-imported,
a new module object is created, and the old module is subject to normal garbage
collection – as with Python modules.
By default, multiple modules created from the same definition should be
independent: changes to one should not affect the others.
This means that all state should be specific to the module object (using e.g.
using <a class="reference internal" href="#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a>), or its contents (such as the module’s
<a class="reference internal" href="../library/stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> or individual classes created with <a class="reference internal" href="type.html#c.PyType_FromSpec" title="PyType_FromSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpec()</span></code></a>).</p>
<p>All modules created using multi-phase initialization are expected to support
<a class="reference internal" href="init.html#sub-interpreter-support"><span class="std std-ref">sub-interpreters</span></a>. Making sure multiple modules
are independent is typically enough to achieve this.</p>
<p>To request multi-phase initialization, the initialization function
(PyInit_modulename) returns a <a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyModuleDef</span></code></a> instance with non-empty
<a class="reference internal" href="#c.PyModuleDef.m_slots" title="PyModuleDef.m_slots"><code class="xref c c-member docutils literal notranslate"><span class="pre">m_slots</span></code></a>. Before it is returned, the <code class="docutils literal notranslate"><span class="pre">PyModuleDef</span></code>
instance must be initialized with the following function:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyModuleDef_Init">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModuleDef_Init</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModuleDef_Init" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: Borrowed reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.5.</em><p>Ensures a module definition is a properly initialized Python object that
correctly reports its type and reference count.</p>
<p>Returns <em>def</em> cast to <code class="docutils literal notranslate"><span class="pre">PyObject*</span></code>, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if an error occurred.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<p>The <em>m_slots</em> member of the module definition must point to an array of
<code class="docutils literal notranslate"><span class="pre">PyModuleDef_Slot</span></code> structures:</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyModuleDef_Slot">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModuleDef_Slot</span></span></span><a class="headerlink" href="#c.PyModuleDef_Slot" title="Link to this definition">¶</a><br /></dt>
<dd><dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef_Slot.slot">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">slot</span></span></span><a class="headerlink" href="#c.PyModuleDef_Slot.slot" title="Link to this definition">¶</a><br /></dt>
<dd><p>A slot ID, chosen from the available values explained below.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyModuleDef_Slot.value">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">value</span></span></span><a class="headerlink" href="#c.PyModuleDef_Slot.value" title="Link to this definition">¶</a><br /></dt>
<dd><p>Value of the slot, whose meaning depends on the slot ID.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<p>The <em>m_slots</em> array must be terminated by a slot with id 0.</p>
<p>The available slot types are:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_mod_create">
<span class="sig-name descname"><span class="n"><span class="pre">Py_mod_create</span></span></span><a class="headerlink" href="#c.Py_mod_create" title="Link to this definition">¶</a><br /></dt>
<dd><p>Specifies a function that is called to create the module object itself.
The <em>value</em> pointer of this slot must point to a function of the signature:</p>
<dl class="c function">
<dt class="sig sig-object c">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">create_module</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">spec</span></span>, <a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><br /></dt>
<dd></dd></dl>

<p>The function receives a <a class="reference internal" href="../library/importlib.html#importlib.machinery.ModuleSpec" title="importlib.machinery.ModuleSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleSpec</span></code></a>
instance, as defined in <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0451/"><strong>PEP 451</strong></a>, and the module definition.
It should return a new module object, or set an error
and return <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>This function should be kept minimal. In particular, it should not
call arbitrary Python code, as trying to import the same module again may
result in an infinite loop.</p>
<p>Multiple <code class="docutils literal notranslate"><span class="pre">Py_mod_create</span></code> slots may not be specified in one module
definition.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">Py_mod_create</span></code> is not specified, the import machinery will create
a normal module object using <a class="reference internal" href="#c.PyModule_New" title="PyModule_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_New()</span></code></a>. The name is taken from
<em>spec</em>, not the definition, to allow extension modules to dynamically adjust
to their place in the module hierarchy and be imported under different
names through symlinks, all while sharing a single module definition.</p>
<p>There is no requirement for the returned object to be an instance of
<a class="reference internal" href="#c.PyModule_Type" title="PyModule_Type"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyModule_Type</span></code></a>. Any type can be used, as long as it supports
setting and getting import-related attributes.
However, only <code class="docutils literal notranslate"><span class="pre">PyModule_Type</span></code> instances may be returned if the
<code class="docutils literal notranslate"><span class="pre">PyModuleDef</span></code> has non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code> <code class="docutils literal notranslate"><span class="pre">m_traverse</span></code>, <code class="docutils literal notranslate"><span class="pre">m_clear</span></code>,
<code class="docutils literal notranslate"><span class="pre">m_free</span></code>; non-zero <code class="docutils literal notranslate"><span class="pre">m_size</span></code>; or slots other than <code class="docutils literal notranslate"><span class="pre">Py_mod_create</span></code>.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_mod_exec">
<span class="sig-name descname"><span class="n"><span class="pre">Py_mod_exec</span></span></span><a class="headerlink" href="#c.Py_mod_exec" title="Link to this definition">¶</a><br /></dt>
<dd><p>Specifies a function that is called to <em>execute</em> the module.
This is equivalent to executing the code of a Python module: typically,
this function adds classes and constants to the module.
The signature of the function is:</p>
<dl class="c function">
<dt class="sig sig-object c">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">exec_module</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span><span class="sig-paren">)</span><br /></dt>
<dd></dd></dl>

<p>If multiple <code class="docutils literal notranslate"><span class="pre">Py_mod_exec</span></code> slots are specified, they are processed in the
order they appear in the <em>m_slots</em> array.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_mod_multiple_interpreters">
<span class="sig-name descname"><span class="n"><span class="pre">Py_mod_multiple_interpreters</span></span></span><a class="headerlink" href="#c.Py_mod_multiple_interpreters" title="Link to this definition">¶</a><br /></dt>
<dd><p>Specifies one of the following values:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_MOD_MULTIPLE_INTERPRETERS_NOT_SUPPORTED">
<span class="sig-name descname"><span class="n"><span class="pre">Py_MOD_MULTIPLE_INTERPRETERS_NOT_SUPPORTED</span></span></span><a class="headerlink" href="#c.Py_MOD_MULTIPLE_INTERPRETERS_NOT_SUPPORTED" title="Link to this definition">¶</a><br /></dt>
<dd><p>The module does not support being imported in subinterpreters.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_MOD_MULTIPLE_INTERPRETERS_SUPPORTED">
<span class="sig-name descname"><span class="n"><span class="pre">Py_MOD_MULTIPLE_INTERPRETERS_SUPPORTED</span></span></span><a class="headerlink" href="#c.Py_MOD_MULTIPLE_INTERPRETERS_SUPPORTED" title="Link to this definition">¶</a><br /></dt>
<dd><p>The module supports being imported in subinterpreters,
but only when they share the main interpreter’s GIL.
(See <a class="reference internal" href="../howto/isolating-extensions.html#isolating-extensions-howto"><span class="std std-ref">Isolating Extension Modules</span></a>.)</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_MOD_PER_INTERPRETER_GIL_SUPPORTED">
<span class="sig-name descname"><span class="n"><span class="pre">Py_MOD_PER_INTERPRETER_GIL_SUPPORTED</span></span></span><a class="headerlink" href="#c.Py_MOD_PER_INTERPRETER_GIL_SUPPORTED" title="Link to this definition">¶</a><br /></dt>
<dd><p>The module supports being imported in subinterpreters,
even when they have their own GIL.
(See <a class="reference internal" href="../howto/isolating-extensions.html#isolating-extensions-howto"><span class="std std-ref">Isolating Extension Modules</span></a>.)</p>
</dd></dl>

<p>This slot determines whether or not importing this module
in a subinterpreter will fail.</p>
<p>Multiple <code class="docutils literal notranslate"><span class="pre">Py_mod_multiple_interpreters</span></code> slots may not be specified
in one module definition.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">Py_mod_multiple_interpreters</span></code> is not specified, the import
machinery defaults to <code class="docutils literal notranslate"><span class="pre">Py_MOD_MULTIPLE_INTERPRETERS_NOT_SUPPORTED</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<p>See <span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a> for more details on multi-phase initialization.</p>
</section>
<section id="low-level-module-creation-functions">
<h3>Low-level module creation functions<a class="headerlink" href="#low-level-module-creation-functions" title="Link to this heading">¶</a></h3>
<p>The following functions are called under the hood when using multi-phase
initialization. They can be used directly, for example when creating module
objects dynamically. Note that both <code class="docutils literal notranslate"><span class="pre">PyModule_FromDefAndSpec</span></code> and
<code class="docutils literal notranslate"><span class="pre">PyModule_ExecDef</span></code> must be called to fully initialize a module.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_FromDefAndSpec">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_FromDefAndSpec</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">spec</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_FromDefAndSpec" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>Create a new module object, given the definition in <em>def</em> and the
ModuleSpec <em>spec</em>.  This behaves like <a class="reference internal" href="#c.PyModule_FromDefAndSpec2" title="PyModule_FromDefAndSpec2"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_FromDefAndSpec2()</span></code></a>
with <em>module_api_version</em> set to <code class="xref c c-macro docutils literal notranslate"><span class="pre">PYTHON_API_VERSION</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_FromDefAndSpec2">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_FromDefAndSpec2</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">spec</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">module_api_version</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_FromDefAndSpec2" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Create a new module object, given the definition in <em>def</em> and the
ModuleSpec <em>spec</em>, assuming the API version <em>module_api_version</em>.
If that version does not match the version of the running interpreter,
a <a class="reference internal" href="../library/exceptions.html#RuntimeWarning" title="RuntimeWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeWarning</span></code></a> is emitted.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Most uses of this function should be using <a class="reference internal" href="#c.PyModule_FromDefAndSpec" title="PyModule_FromDefAndSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_FromDefAndSpec()</span></code></a>
instead; only use this if you are sure you need it.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_ExecDef">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_ExecDef</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_ExecDef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Process any execution slots (<a class="reference internal" href="#c.Py_mod_exec" title="Py_mod_exec"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_mod_exec</span></code></a>) given in <em>def</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_SetDocString">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_SetDocString</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">docstring</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_SetDocString" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Set the docstring for <em>module</em> to <em>docstring</em>.
This function is called automatically when creating a module from
<code class="docutils literal notranslate"><span class="pre">PyModuleDef</span></code>, using either <code class="docutils literal notranslate"><span class="pre">PyModule_Create</span></code> or
<code class="docutils literal notranslate"><span class="pre">PyModule_FromDefAndSpec</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_AddFunctions">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddFunctions</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><span class="n"><span class="pre">PyMethodDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">functions</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddFunctions" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Add the functions from the <code class="docutils literal notranslate"><span class="pre">NULL</span></code> terminated <em>functions</em> array to <em>module</em>.
Refer to the <a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMethodDef</span></code></a> documentation for details on individual
entries (due to the lack of a shared module namespace, module level
“functions” implemented in C typically receive the module as their first
parameter, making them similar to instance methods on Python classes).
This function is called automatically when creating a module from
<code class="docutils literal notranslate"><span class="pre">PyModuleDef</span></code>, using either <code class="docutils literal notranslate"><span class="pre">PyModule_Create</span></code> or
<code class="docutils literal notranslate"><span class="pre">PyModule_FromDefAndSpec</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

</section>
<section id="support-functions">
<h3>Support functions<a class="headerlink" href="#support-functions" title="Link to this heading">¶</a></h3>
<p>The module initialization function (if using single phase initialization) or
a function called from a module execution slot (if using multi-phase
initialization), can use the following functions to help initialize the module
state:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_AddObjectRef">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddObjectRef</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddObjectRef" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Add an object to <em>module</em> as <em>name</em>.  This is a convenience function which
can be used from the module’s initialization function.</p>
<p>On success, return <code class="docutils literal notranslate"><span class="pre">0</span></code>. On error, raise an exception and return <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
<p>Return <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if <em>value</em> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. It must be called with an exception
raised in this case.</p>
<p>Example usage:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">int</span>
<span class="nf">add_spam</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">obj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyLong_FromLong</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">obj</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="nb">NULL</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">-1</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="kt">int</span><span class="w"> </span><span class="n">res</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyModule_AddObjectRef</span><span class="p">(</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;spam&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">obj</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_DECREF</span><span class="p">(</span><span class="n">obj</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">res</span><span class="p">;</span>
<span class="w"> </span><span class="p">}</span>
</pre></div>
</div>
<p>The example can also be written without checking explicitly if <em>obj</em> is
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">int</span>
<span class="nf">add_spam</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">obj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyLong_FromLong</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
<span class="w">    </span><span class="kt">int</span><span class="w"> </span><span class="n">res</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyModule_AddObjectRef</span><span class="p">(</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;spam&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">obj</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_XDECREF</span><span class="p">(</span><span class="n">obj</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">res</span><span class="p">;</span>
<span class="w"> </span><span class="p">}</span>
</pre></div>
</div>
<p>Note that <code class="docutils literal notranslate"><span class="pre">Py_XDECREF()</span></code> should be used instead of <code class="docutils literal notranslate"><span class="pre">Py_DECREF()</span></code> in
this case, since <em>obj</em> can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_AddObject">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddObject</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddObject" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Similar to <a class="reference internal" href="#c.PyModule_AddObjectRef" title="PyModule_AddObjectRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddObjectRef()</span></code></a>, but steals a reference to
<em>value</em> on success (if it returns <code class="docutils literal notranslate"><span class="pre">0</span></code>).</p>
<p>The new <a class="reference internal" href="#c.PyModule_AddObjectRef" title="PyModule_AddObjectRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddObjectRef()</span></code></a> function is recommended, since it is
easy to introduce reference leaks by misusing the
<a class="reference internal" href="#c.PyModule_AddObject" title="PyModule_AddObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddObject()</span></code></a> function.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike other functions that steal references, <code class="docutils literal notranslate"><span class="pre">PyModule_AddObject()</span></code>
only releases the reference to <em>value</em> <strong>on success</strong>.</p>
<p>This means that its return value must be checked, and calling code must
<a class="reference internal" href="refcounting.html#c.Py_DECREF" title="Py_DECREF"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DECREF()</span></code></a> <em>value</em> manually on error.</p>
</div>
<p>Example usage:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">int</span>
<span class="nf">add_spam</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">obj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyLong_FromLong</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">obj</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="nb">NULL</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">-1</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyModule_AddObject</span><span class="p">(</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;spam&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">obj</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Py_DECREF</span><span class="p">(</span><span class="n">obj</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">-1</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="c1">// PyModule_AddObject() stole a reference to obj:</span>
<span class="w">    </span><span class="c1">// Py_DECREF(obj) is not needed here</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The example can also be written without checking explicitly if <em>obj</em> is
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">static</span><span class="w"> </span><span class="kt">int</span>
<span class="nf">add_spam</span><span class="p">(</span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">value</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyObject</span><span class="w"> </span><span class="o">*</span><span class="n">obj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyLong_FromLong</span><span class="p">(</span><span class="n">value</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyModule_AddObject</span><span class="p">(</span><span class="n">module</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;spam&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">obj</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Py_XDECREF</span><span class="p">(</span><span class="n">obj</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">-1</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="c1">// PyModule_AddObject() stole a reference to obj:</span>
<span class="w">    </span><span class="c1">// Py_DECREF(obj) is not needed here</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>Note that <code class="docutils literal notranslate"><span class="pre">Py_XDECREF()</span></code> should be used instead of <code class="docutils literal notranslate"><span class="pre">Py_DECREF()</span></code> in
this case, since <em>obj</em> can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_AddIntConstant">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddIntConstant</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddIntConstant" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Add an integer constant to <em>module</em> as <em>name</em>.  This convenience function can be
used from the module’s initialization function. Return <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error, <code class="docutils literal notranslate"><span class="pre">0</span></code> on
success.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_AddStringConstant">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddStringConstant</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddStringConstant" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Add a string constant to <em>module</em> as <em>name</em>.  This convenience function can be
used from the module’s initialization function.  The string <em>value</em> must be
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>-terminated.  Return <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error, <code class="docutils literal notranslate"><span class="pre">0</span></code> on success.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyModule_AddIntMacro">
<span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddIntMacro</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">module</span></span>, <span class="n"><span class="pre">macro</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddIntMacro" title="Link to this definition">¶</a><br /></dt>
<dd><p>Add an int constant to <em>module</em>. The name and the value are taken from
<em>macro</em>. For example <code class="docutils literal notranslate"><span class="pre">PyModule_AddIntMacro(module,</span> <span class="pre">AF_INET)</span></code> adds the int
constant <em>AF_INET</em> with the value of <em>AF_INET</em> to <em>module</em>.
Return <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error, <code class="docutils literal notranslate"><span class="pre">0</span></code> on success.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyModule_AddStringMacro">
<span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddStringMacro</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">module</span></span>, <span class="n"><span class="pre">macro</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddStringMacro" title="Link to this definition">¶</a><br /></dt>
<dd><p>Add a string constant to <em>module</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyModule_AddType">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyModule_AddType</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">type</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyModule_AddType" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Add a type object to <em>module</em>.
The type object is finalized by calling internally <a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a>.
The name of the type object is taken from the last component of
<a class="reference internal" href="typeobj.html#c.PyTypeObject.tp_name" title="PyTypeObject.tp_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">tp_name</span></code></a> after dot.
Return <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error, <code class="docutils literal notranslate"><span class="pre">0</span></code> on success.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

</section>
</section>
<section id="module-lookup">
<h2>Module lookup<a class="headerlink" href="#module-lookup" title="Link to this heading">¶</a></h2>
<p>Single-phase initialization creates singleton modules that can be looked up
in the context of the current interpreter. This allows the module object to be
retrieved later with only a reference to the module definition.</p>
<p>These functions will not work on modules created using multi-phase initialization,
since multiple such modules can be created from a single definition.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyState_FindModule">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyState_FindModule</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyState_FindModule" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: Borrowed reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Returns the module object that was created from <em>def</em> for the current interpreter.
This method requires that the module object has been attached to the interpreter state with
<a class="reference internal" href="#c.PyState_AddModule" title="PyState_AddModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyState_AddModule()</span></code></a> beforehand. In case the corresponding module object is not
found or has not been attached to the interpreter state yet, it returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyState_AddModule">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyState_AddModule</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">module</span></span>, <a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyState_AddModule" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.3.</em><p>Attaches the module object passed to the function to the interpreter state. This allows
the module object to be accessible via <a class="reference internal" href="#c.PyState_FindModule" title="PyState_FindModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyState_FindModule()</span></code></a>.</p>
<p>Only effective on modules created using single-phase initialization.</p>
<p>Python calls <code class="docutils literal notranslate"><span class="pre">PyState_AddModule</span></code> automatically after importing a module,
so it is unnecessary (but harmless) to call it from module initialization
code. An explicit call is needed only if the module’s own init code
subsequently calls <code class="docutils literal notranslate"><span class="pre">PyState_FindModule</span></code>.
The function is mainly intended for implementing alternative import
mechanisms (either by calling it directly, or by referring to its
implementation for details of the required state updates).</p>
<p>The caller must hold the GIL.</p>
<p>Return 0 on success or -1 on failure.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyState_RemoveModule">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyState_RemoveModule</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyModuleDef" title="PyModuleDef"><span class="n"><span class="pre">PyModuleDef</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">def</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyState_RemoveModule" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.3.</em><p>Removes the module object created from <em>def</em> from the interpreter state.
Return 0 on success or -1 on failure.</p>
<p>The caller must hold the GIL.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Module Objects</a><ul>
<li><a class="reference internal" href="#initializing-c-modules">Initializing C modules</a><ul>
<li><a class="reference internal" href="#single-phase-initialization">Single-phase initialization</a></li>
<li><a class="reference internal" href="#multi-phase-initialization">Multi-phase initialization</a></li>
<li><a class="reference internal" href="#low-level-module-creation-functions">Low-level module creation functions</a></li>
<li><a class="reference internal" href="#support-functions">Support functions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-lookup">Module lookup</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="file.html"
                          title="previous chapter">File Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="iterator.html"
                          title="next chapter">Iterator Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/module.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="iterator.html" title="Iterator Objects"
             >next</a> |</li>
        <li class="right" >
          <a href="file.html" title="File Objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Module Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>