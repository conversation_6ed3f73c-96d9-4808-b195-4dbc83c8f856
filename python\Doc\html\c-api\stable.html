<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="C API Stability" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/stable.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Unless documented otherwise, Python’s C API is covered by the Backwards Compatibility Policy, PEP 387. Most changes to it are source-compatible (typically by only adding new API). Changing existing..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Unless documented otherwise, Python’s C API is covered by the Backwards Compatibility Policy, PEP 387. Most changes to it are source-compatible (typically by only adding new API). Changing existing..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>C API Stability &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="The Very High Level Layer" href="veryhigh.html" />
    <link rel="prev" title="Introduction" href="intro.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/stable.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">C API Stability</a><ul>
<li><a class="reference internal" href="#unstable-c-api">Unstable C API</a></li>
<li><a class="reference internal" href="#stable-application-binary-interface">Stable Application Binary Interface</a><ul>
<li><a class="reference internal" href="#limited-c-api">Limited C API</a></li>
<li><a class="reference internal" href="#stable-abi">Stable ABI</a></li>
<li><a class="reference internal" href="#limited-api-scope-and-performance">Limited API Scope and Performance</a></li>
<li><a class="reference internal" href="#limited-api-caveats">Limited API Caveats</a></li>
</ul>
</li>
<li><a class="reference internal" href="#platform-considerations">Platform Considerations</a></li>
<li><a class="reference internal" href="#contents-of-limited-api">Contents of Limited API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="intro.html"
                          title="previous chapter">Introduction</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="veryhigh.html"
                          title="next chapter">The Very High Level Layer</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/stable.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="veryhigh.html" title="The Very High Level Layer"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">C API Stability</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="c-api-stability">
<span id="stable"></span><h1>C API Stability<a class="headerlink" href="#c-api-stability" title="Link to this heading">¶</a></h1>
<p>Unless documented otherwise, Python’s C API is covered by the Backwards
Compatibility Policy, <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0387/"><strong>PEP 387</strong></a>.
Most changes to it are source-compatible (typically by only adding new API).
Changing existing API or removing API is only done after a deprecation period
or to fix serious issues.</p>
<p>CPython’s Application Binary Interface (ABI) is forward- and
backwards-compatible across a minor release (if these are compiled the same
way; see <a class="reference internal" href="#stable-abi-platform"><span class="std std-ref">Platform Considerations</span></a> below).
So, code compiled for Python 3.10.0 will work on 3.10.8 and vice versa,
but will need to be compiled separately for 3.9.x and 3.11.x.</p>
<p>There are two tiers of C API with different stability expectations:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#unstable-c-api"><span class="std std-ref">Unstable API</span></a>, may change in minor versions without
a deprecation period. It is marked by the <code class="docutils literal notranslate"><span class="pre">PyUnstable</span></code> prefix in names.</p></li>
<li><p><a class="reference internal" href="#limited-c-api"><span class="std std-ref">Limited API</span></a>, is compatible across several minor releases.
When <a class="reference internal" href="#c.Py_LIMITED_API" title="Py_LIMITED_API"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code></a> is defined, only this subset is exposed
from <code class="docutils literal notranslate"><span class="pre">Python.h</span></code>.</p></li>
</ul>
<p>These are discussed in more detail below.</p>
<p>Names prefixed by an underscore, such as <code class="docutils literal notranslate"><span class="pre">_Py_InternalState</span></code>,
are private API that can change without notice even in patch releases.
If you need to use this API, consider reaching out to
<a class="reference external" href="https://discuss.python.org/c/core-dev/c-api/30">CPython developers</a>
to discuss adding public API for your use case.</p>
<section id="unstable-c-api">
<span id="id1"></span><h2>Unstable C API<a class="headerlink" href="#unstable-c-api" title="Link to this heading">¶</a></h2>
<p id="index-1">Any API named with the <code class="docutils literal notranslate"><span class="pre">PyUnstable</span></code> prefix exposes CPython implementation
details, and may change in every minor release (e.g. from 3.9 to 3.10) without
any deprecation warnings.
However, it will not change in a bugfix release (e.g. from 3.10.0 to 3.10.1).</p>
<p>It is generally intended for specialized, low-level tools like debuggers.</p>
<p>Projects that use this API are expected to follow
CPython development and spend extra effort adjusting to changes.</p>
</section>
<section id="stable-application-binary-interface">
<h2>Stable Application Binary Interface<a class="headerlink" href="#stable-application-binary-interface" title="Link to this heading">¶</a></h2>
<p>For simplicity, this document talks about <em>extensions</em>, but the Limited API
and Stable ABI work the same way for all uses of the API – for example,
embedding Python.</p>
<section id="limited-c-api">
<span id="id2"></span><h3>Limited C API<a class="headerlink" href="#limited-c-api" title="Link to this heading">¶</a></h3>
<p>Python 3.2 introduced the <em>Limited API</em>, a subset of Python’s C API.
Extensions that only use the Limited API can be
compiled once and work with multiple versions of Python.
Contents of the Limited API are <a class="reference internal" href="#limited-api-list"><span class="std std-ref">listed below</span></a>.</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_LIMITED_API">
<span class="sig-name descname"><span class="n"><span class="pre">Py_LIMITED_API</span></span></span><a class="headerlink" href="#c.Py_LIMITED_API" title="Link to this definition">¶</a><br /></dt>
<dd><p>Define this macro before including <code class="docutils literal notranslate"><span class="pre">Python.h</span></code> to opt in to only use
the Limited API, and to select the Limited API version.</p>
<p>Define <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> to the value of <a class="reference internal" href="apiabiversion.html#c.PY_VERSION_HEX" title="PY_VERSION_HEX"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_VERSION_HEX</span></code></a>
corresponding to the lowest Python version your extension supports.
The extension will work without recompilation with all Python 3 releases
from the specified one onward, and can use Limited API introduced up to that
version.</p>
<p>Rather than using the <code class="docutils literal notranslate"><span class="pre">PY_VERSION_HEX</span></code> macro directly, hardcode a minimum
minor version (e.g. <code class="docutils literal notranslate"><span class="pre">0x030A0000</span></code> for Python 3.10) for stability when
compiling with future Python versions.</p>
<p>You can also define <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> to <code class="docutils literal notranslate"><span class="pre">3</span></code>. This works the same as
<code class="docutils literal notranslate"><span class="pre">0x03020000</span></code> (Python 3.2, the version that introduced Limited API).</p>
</dd></dl>

</section>
<section id="stable-abi">
<span id="id3"></span><h3>Stable ABI<a class="headerlink" href="#stable-abi" title="Link to this heading">¶</a></h3>
<p>To enable this, Python provides a <em>Stable ABI</em>: a set of symbols that will
remain compatible across Python 3.x versions.</p>
<p>The Stable ABI contains symbols exposed in the <a class="reference internal" href="#limited-c-api"><span class="std std-ref">Limited API</span></a>, but also other ones – for example, functions necessary to
support older versions of the Limited API.</p>
<p>On Windows, extensions that use the Stable ABI should be linked against
<code class="docutils literal notranslate"><span class="pre">python3.dll</span></code> rather than a version-specific library such as
<code class="docutils literal notranslate"><span class="pre">python39.dll</span></code>.</p>
<p>On some platforms, Python will look for and load shared library files named
with the <code class="docutils literal notranslate"><span class="pre">abi3</span></code> tag (e.g. <code class="docutils literal notranslate"><span class="pre">mymodule.abi3.so</span></code>).
It does not check if such extensions conform to a Stable ABI.
The user (or their packaging tools) need to ensure that, for example,
extensions built with the 3.10+ Limited API are not installed for lower
versions of Python.</p>
<p>All functions in the Stable ABI are present as functions in Python’s shared
library, not solely as macros. This makes them usable from languages that don’t
use the C preprocessor.</p>
</section>
<section id="limited-api-scope-and-performance">
<h3>Limited API Scope and Performance<a class="headerlink" href="#limited-api-scope-and-performance" title="Link to this heading">¶</a></h3>
<p>The goal for the Limited API is to allow everything that is possible with the
full C API, but possibly with a performance penalty.</p>
<p>For example, while <a class="reference internal" href="list.html#c.PyList_GetItem" title="PyList_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_GetItem()</span></code></a> is available, its “unsafe” macro
variant <a class="reference internal" href="list.html#c.PyList_GET_ITEM" title="PyList_GET_ITEM"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_GET_ITEM()</span></code></a> is not.
The macro can be faster because it can rely on version-specific implementation
details of the list object.</p>
<p>Without <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> defined, some C API functions are inlined or
replaced by macros.
Defining <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> disables this inlining, allowing stability as
Python’s data structures are improved, but possibly reducing performance.</p>
<p>By leaving out the <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> definition, it is possible to compile
a Limited API extension with a version-specific ABI. This can improve
performance for that Python version, but will limit compatibility.
Compiling with <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> will then yield an extension that can be
distributed where a version-specific one is not available – for example,
for prereleases of an upcoming Python version.</p>
</section>
<section id="limited-api-caveats">
<h3>Limited API Caveats<a class="headerlink" href="#limited-api-caveats" title="Link to this heading">¶</a></h3>
<p>Note that compiling with <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> is <em>not</em> a complete guarantee that
code conforms to the <a class="reference internal" href="#limited-c-api"><span class="std std-ref">Limited API</span></a> or the <a class="reference internal" href="#stable-abi"><span class="std std-ref">Stable ABI</span></a>. <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> only covers definitions, but an API also
includes other issues, such as expected semantics.</p>
<p>One issue that <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> does not guard against is calling a function
with arguments that are invalid in a lower Python version.
For example, consider a function that starts accepting <code class="docutils literal notranslate"><span class="pre">NULL</span></code> for an
argument. In Python 3.9, <code class="docutils literal notranslate"><span class="pre">NULL</span></code> now selects a default behavior, but in
Python 3.8, the argument will be used directly, causing a <code class="docutils literal notranslate"><span class="pre">NULL</span></code> dereference
and crash. A similar argument works for fields of structs.</p>
<p>Another issue is that some struct fields are currently not hidden when
<code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> is defined, even though they’re part of the Limited API.</p>
<p>For these reasons, we recommend testing an extension with <em>all</em> minor Python
versions it supports, and preferably to build with the <em>lowest</em> such version.</p>
<p>We also recommend reviewing documentation of all used API to check
if it is explicitly part of the Limited API. Even with <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code>
defined, a few private declarations are exposed for technical reasons (or
even unintentionally, as bugs).</p>
<p>Also note that the Limited API is not necessarily stable: compiling with
<code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> with Python 3.8 means that the extension will
run with Python 3.12, but it will not necessarily <em>compile</em> with Python 3.12.
In particular, parts of the Limited API may be deprecated and removed,
provided that the Stable ABI stays stable.</p>
</section>
</section>
<section id="platform-considerations">
<span id="stable-abi-platform"></span><h2>Platform Considerations<a class="headerlink" href="#platform-considerations" title="Link to this heading">¶</a></h2>
<p>ABI stability depends not only on Python, but also on the compiler used,
lower-level libraries and compiler options. For the purposes of
the <a class="reference internal" href="#stable-abi"><span class="std std-ref">Stable ABI</span></a>, these details define a “platform”. They
usually depend on the OS type and processor architecture</p>
<p>It is the responsibility of each particular distributor of Python
to ensure that all Python versions on a particular platform are built
in a way that does not break the Stable ABI.
This is the case with Windows and macOS releases from <code class="docutils literal notranslate"><span class="pre">python.org</span></code> and many
third-party distributors.</p>
</section>
<section id="contents-of-limited-api">
<span id="limited-api-list"></span><h2>Contents of Limited API<a class="headerlink" href="#contents-of-limited-api" title="Link to this heading">¶</a></h2>
<p>Currently, the <a class="reference internal" href="#limited-c-api"><span class="std std-ref">Limited API</span></a> includes the following items:</p>
<p><ul class="simple">
<li><p><a class="reference internal" href="call.html#c.PY_VECTORCALL_ARGUMENTS_OFFSET" title="PY_VECTORCALL_ARGUMENTS_OFFSET"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_VECTORCALL_ARGUMENTS_OFFSET</span></code></a></p></li>
<li><p><a class="reference internal" href="iter.html#c.PyAIter_Check" title="PyAIter_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyAIter_Check()</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.PyArg_Parse" title="PyArg_Parse"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_Parse()</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.PyArg_ParseTuple" title="PyArg_ParseTuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_ParseTuple()</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.PyArg_ParseTupleAndKeywords" title="PyArg_ParseTupleAndKeywords"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_ParseTupleAndKeywords()</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.PyArg_UnpackTuple" title="PyArg_UnpackTuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_UnpackTuple()</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.PyArg_VaParse" title="PyArg_VaParse"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_VaParse()</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.PyArg_VaParseTupleAndKeywords" title="PyArg_VaParseTupleAndKeywords"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_VaParseTupleAndKeywords()</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.PyArg_ValidateKeywordArguments" title="PyArg_ValidateKeywordArguments"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_ValidateKeywordArguments()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBaseObject_Type</span></code></p></li>
<li><p><a class="reference internal" href="bool.html#c.PyBool_FromLong" title="PyBool_FromLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBool_FromLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="bool.html#c.PyBool_Type" title="PyBool_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBool_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_FillContiguousStrides" title="PyBuffer_FillContiguousStrides"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_FillContiguousStrides()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_FillInfo" title="PyBuffer_FillInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_FillInfo()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_FromContiguous" title="PyBuffer_FromContiguous"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_FromContiguous()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_GetPointer" title="PyBuffer_GetPointer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_GetPointer()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_IsContiguous" title="PyBuffer_IsContiguous"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_IsContiguous()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_Release" title="PyBuffer_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_Release()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_SizeFromFormat" title="PyBuffer_SizeFromFormat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_SizeFromFormat()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyBuffer_ToContiguous" title="PyBuffer_ToContiguous"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_ToContiguous()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyByteArrayIter_Type</span></code></p></li>
<li><p><a class="reference internal" href="bytearray.html#c.PyByteArray_AsString" title="PyByteArray_AsString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyByteArray_AsString()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytearray.html#c.PyByteArray_Concat" title="PyByteArray_Concat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyByteArray_Concat()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytearray.html#c.PyByteArray_FromObject" title="PyByteArray_FromObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyByteArray_FromObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytearray.html#c.PyByteArray_FromStringAndSize" title="PyByteArray_FromStringAndSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyByteArray_FromStringAndSize()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytearray.html#c.PyByteArray_Resize" title="PyByteArray_Resize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyByteArray_Resize()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytearray.html#c.PyByteArray_Size" title="PyByteArray_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyByteArray_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytearray.html#c.PyByteArray_Type" title="PyByteArray_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyByteArray_Type</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBytesIter_Type</span></code></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_AsString" title="PyBytes_AsString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_AsString()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_AsStringAndSize" title="PyBytes_AsStringAndSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_AsStringAndSize()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_Concat" title="PyBytes_Concat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_Concat()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_ConcatAndDel" title="PyBytes_ConcatAndDel"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_ConcatAndDel()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_DecodeEscape()</span></code></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_FromFormat" title="PyBytes_FromFormat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_FromFormat()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_FromFormatV" title="PyBytes_FromFormatV"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_FromFormatV()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_FromObject" title="PyBytes_FromObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_FromObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_FromString" title="PyBytes_FromString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_FromString()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_FromStringAndSize" title="PyBytes_FromStringAndSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_FromStringAndSize()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_Repr()</span></code></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_Size" title="PyBytes_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBytes_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="bytes.html#c.PyBytes_Type" title="PyBytes_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyBytes_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyCFunction" title="PyCFunction"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunction</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyCFunctionWithKeywords" title="PyCFunctionWithKeywords"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCFunctionWithKeywords</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCFunction_Call()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCFunction_GetFlags()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCFunction_GetFunction()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCFunction_GetSelf()</span></code></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyCFunction_New" title="PyCFunction_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCFunction_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyCFunction_NewEx" title="PyCFunction_NewEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCFunction_NewEx()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyCFunction_Type</span></code></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyCMethod_New" title="PyCMethod_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCMethod_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="iterator.html#c.PyCallIter_New" title="PyCallIter_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCallIter_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="iterator.html#c.PyCallIter_Type" title="PyCallIter_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyCallIter_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyCallable_Check" title="PyCallable_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCallable_Check()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_Destructor" title="PyCapsule_Destructor"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyCapsule_Destructor</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_GetContext" title="PyCapsule_GetContext"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_GetContext()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_GetDestructor" title="PyCapsule_GetDestructor"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_GetDestructor()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_GetName" title="PyCapsule_GetName"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_GetName()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_GetPointer" title="PyCapsule_GetPointer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_GetPointer()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_Import" title="PyCapsule_Import"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_Import()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_IsValid" title="PyCapsule_IsValid"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_IsValid()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_New" title="PyCapsule_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_SetContext" title="PyCapsule_SetContext"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_SetContext()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_SetDestructor" title="PyCapsule_SetDestructor"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_SetDestructor()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_SetName" title="PyCapsule_SetName"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_SetName()</span></code></a></p></li>
<li><p><a class="reference internal" href="capsule.html#c.PyCapsule_SetPointer" title="PyCapsule_SetPointer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCapsule_SetPointer()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyCapsule_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyClassMethodDescr_Type</span></code></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_BackslashReplaceErrors" title="PyCodec_BackslashReplaceErrors"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_BackslashReplaceErrors()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_Decode" title="PyCodec_Decode"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_Decode()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_Decoder" title="PyCodec_Decoder"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_Decoder()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_Encode" title="PyCodec_Encode"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_Encode()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_Encoder" title="PyCodec_Encoder"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_Encoder()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_IgnoreErrors" title="PyCodec_IgnoreErrors"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_IgnoreErrors()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_IncrementalDecoder" title="PyCodec_IncrementalDecoder"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_IncrementalDecoder()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_IncrementalEncoder" title="PyCodec_IncrementalEncoder"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_IncrementalEncoder()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_KnownEncoding" title="PyCodec_KnownEncoding"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_KnownEncoding()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_LookupError" title="PyCodec_LookupError"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_LookupError()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_NameReplaceErrors" title="PyCodec_NameReplaceErrors"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_NameReplaceErrors()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_Register" title="PyCodec_Register"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_Register()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_RegisterError" title="PyCodec_RegisterError"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_RegisterError()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_ReplaceErrors" title="PyCodec_ReplaceErrors"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_ReplaceErrors()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_StreamReader" title="PyCodec_StreamReader"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_StreamReader()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_StreamWriter" title="PyCodec_StreamWriter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_StreamWriter()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_StrictErrors" title="PyCodec_StrictErrors"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_StrictErrors()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_Unregister" title="PyCodec_Unregister"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_Unregister()</span></code></a></p></li>
<li><p><a class="reference internal" href="codec.html#c.PyCodec_XMLCharRefReplaceErrors" title="PyCodec_XMLCharRefReplaceErrors"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyCodec_XMLCharRefReplaceErrors()</span></code></a></p></li>
<li><p><a class="reference internal" href="complex.html#c.PyComplex_FromDoubles" title="PyComplex_FromDoubles"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyComplex_FromDoubles()</span></code></a></p></li>
<li><p><a class="reference internal" href="complex.html#c.PyComplex_ImagAsDouble" title="PyComplex_ImagAsDouble"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyComplex_ImagAsDouble()</span></code></a></p></li>
<li><p><a class="reference internal" href="complex.html#c.PyComplex_RealAsDouble" title="PyComplex_RealAsDouble"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyComplex_RealAsDouble()</span></code></a></p></li>
<li><p><a class="reference internal" href="complex.html#c.PyComplex_Type" title="PyComplex_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyComplex_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="descriptor.html#c.PyDescr_NewClassMethod" title="PyDescr_NewClassMethod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDescr_NewClassMethod()</span></code></a></p></li>
<li><p><a class="reference internal" href="descriptor.html#c.PyDescr_NewGetSet" title="PyDescr_NewGetSet"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDescr_NewGetSet()</span></code></a></p></li>
<li><p><a class="reference internal" href="descriptor.html#c.PyDescr_NewMember" title="PyDescr_NewMember"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDescr_NewMember()</span></code></a></p></li>
<li><p><a class="reference internal" href="descriptor.html#c.PyDescr_NewMethod" title="PyDescr_NewMethod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDescr_NewMethod()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictItems_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictIterItem_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictIterKey_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictIterValue_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictKeys_Type</span></code></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDictProxy_New" title="PyDictProxy_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDictProxy_New()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictProxy_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictRevIterItem_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictRevIterKey_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictRevIterValue_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDictValues_Type</span></code></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Clear" title="PyDict_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Clear()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Contains" title="PyDict_Contains"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Contains()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Copy" title="PyDict_Copy"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Copy()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_DelItem" title="PyDict_DelItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_DelItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_DelItemString" title="PyDict_DelItemString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_DelItemString()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_GetItem" title="PyDict_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_GetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_GetItemString" title="PyDict_GetItemString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_GetItemString()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_GetItemWithError" title="PyDict_GetItemWithError"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_GetItemWithError()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Items" title="PyDict_Items"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Items()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Keys" title="PyDict_Keys"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Keys()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Merge" title="PyDict_Merge"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Merge()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_MergeFromSeq2" title="PyDict_MergeFromSeq2"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_MergeFromSeq2()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_New" title="PyDict_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Next" title="PyDict_Next"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Next()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_SetItem" title="PyDict_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_SetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_SetItemString" title="PyDict_SetItemString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_SetItemString()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Size" title="PyDict_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Type" title="PyDict_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyDict_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Update" title="PyDict_Update"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Update()</span></code></a></p></li>
<li><p><a class="reference internal" href="dict.html#c.PyDict_Values" title="PyDict_Values"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyDict_Values()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyEllipsis_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyEnum_Type</span></code></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_BadArgument" title="PyErr_BadArgument"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_BadArgument()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_BadInternalCall" title="PyErr_BadInternalCall"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_BadInternalCall()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_CheckSignals" title="PyErr_CheckSignals"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_CheckSignals()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_Clear" title="PyErr_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Clear()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Display()</span></code></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_DisplayException" title="PyErr_DisplayException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_DisplayException()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_ExceptionMatches" title="PyErr_ExceptionMatches"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_ExceptionMatches()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_Fetch" title="PyErr_Fetch"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Fetch()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_Format" title="PyErr_Format"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Format()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_FormatV" title="PyErr_FormatV"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_FormatV()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_GetExcInfo" title="PyErr_GetExcInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetExcInfo()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_GetHandledException" title="PyErr_GetHandledException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetHandledException()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_GetRaisedException" title="PyErr_GetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GetRaisedException()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_GivenExceptionMatches" title="PyErr_GivenExceptionMatches"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_GivenExceptionMatches()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_NewException" title="PyErr_NewException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_NewException()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_NewExceptionWithDoc" title="PyErr_NewExceptionWithDoc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_NewExceptionWithDoc()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_NoMemory" title="PyErr_NoMemory"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_NoMemory()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_NormalizeException" title="PyErr_NormalizeException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_NormalizeException()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_Occurred" title="PyErr_Occurred"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Occurred()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_Print" title="PyErr_Print"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Print()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_PrintEx" title="PyErr_PrintEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_PrintEx()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_ProgramText()</span></code></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_ResourceWarning" title="PyErr_ResourceWarning"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_ResourceWarning()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_Restore" title="PyErr_Restore"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_Restore()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetExcFromWindowsErr" title="PyErr_SetExcFromWindowsErr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetExcFromWindowsErr()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetExcFromWindowsErrWithFilename" title="PyErr_SetExcFromWindowsErrWithFilename"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetExcFromWindowsErrWithFilename()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetExcFromWindowsErrWithFilenameObject" title="PyErr_SetExcFromWindowsErrWithFilenameObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetExcFromWindowsErrWithFilenameObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetExcFromWindowsErrWithFilenameObjects" title="PyErr_SetExcFromWindowsErrWithFilenameObjects"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetExcFromWindowsErrWithFilenameObjects()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetExcInfo" title="PyErr_SetExcInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetExcInfo()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetFromErrno" title="PyErr_SetFromErrno"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetFromErrno()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetFromErrnoWithFilename" title="PyErr_SetFromErrnoWithFilename"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetFromErrnoWithFilename()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetFromErrnoWithFilenameObject" title="PyErr_SetFromErrnoWithFilenameObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetFromErrnoWithFilenameObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetFromErrnoWithFilenameObjects" title="PyErr_SetFromErrnoWithFilenameObjects"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetFromErrnoWithFilenameObjects()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetFromWindowsErr" title="PyErr_SetFromWindowsErr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetFromWindowsErr()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetFromWindowsErrWithFilename" title="PyErr_SetFromWindowsErrWithFilename"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetFromWindowsErrWithFilename()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetHandledException" title="PyErr_SetHandledException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetHandledException()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetImportError" title="PyErr_SetImportError"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetImportError()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetImportErrorSubclass" title="PyErr_SetImportErrorSubclass"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetImportErrorSubclass()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetInterrupt" title="PyErr_SetInterrupt"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetInterrupt()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetInterruptEx" title="PyErr_SetInterruptEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetInterruptEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetNone" title="PyErr_SetNone"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetNone()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetObject" title="PyErr_SetObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetRaisedException" title="PyErr_SetRaisedException"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetRaisedException()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SetString" title="PyErr_SetString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SetString()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SyntaxLocation" title="PyErr_SyntaxLocation"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SyntaxLocation()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_SyntaxLocationEx" title="PyErr_SyntaxLocationEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_SyntaxLocationEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_WarnEx" title="PyErr_WarnEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_WarnEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_WarnExplicit" title="PyErr_WarnExplicit"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_WarnExplicit()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_WarnFormat" title="PyErr_WarnFormat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_WarnFormat()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyErr_WriteUnraisable" title="PyErr_WriteUnraisable"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyErr_WriteUnraisable()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_AcquireLock" title="PyEval_AcquireLock"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_AcquireLock()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_AcquireThread" title="PyEval_AcquireThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_AcquireThread()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_CallFunction()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_CallMethod()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_CallObjectWithKeywords()</span></code></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.PyEval_EvalCode" title="PyEval_EvalCode"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_EvalCode()</span></code></a></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.PyEval_EvalCodeEx" title="PyEval_EvalCodeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_EvalCodeEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.PyEval_EvalFrame" title="PyEval_EvalFrame"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_EvalFrame()</span></code></a></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.PyEval_EvalFrameEx" title="PyEval_EvalFrameEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_EvalFrameEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="reflection.html#c.PyEval_GetBuiltins" title="PyEval_GetBuiltins"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_GetBuiltins()</span></code></a></p></li>
<li><p><a class="reference internal" href="reflection.html#c.PyEval_GetFrame" title="PyEval_GetFrame"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_GetFrame()</span></code></a></p></li>
<li><p><a class="reference internal" href="reflection.html#c.PyEval_GetFuncDesc" title="PyEval_GetFuncDesc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_GetFuncDesc()</span></code></a></p></li>
<li><p><a class="reference internal" href="reflection.html#c.PyEval_GetFuncName" title="PyEval_GetFuncName"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_GetFuncName()</span></code></a></p></li>
<li><p><a class="reference internal" href="reflection.html#c.PyEval_GetGlobals" title="PyEval_GetGlobals"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_GetGlobals()</span></code></a></p></li>
<li><p><a class="reference internal" href="reflection.html#c.PyEval_GetLocals" title="PyEval_GetLocals"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_GetLocals()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_InitThreads" title="PyEval_InitThreads"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_InitThreads()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_ReleaseLock" title="PyEval_ReleaseLock"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_ReleaseLock()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_ReleaseThread" title="PyEval_ReleaseThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_ReleaseThread()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_RestoreThread" title="PyEval_RestoreThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_RestoreThread()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_SaveThread" title="PyEval_SaveThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SaveThread()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyEval_ThreadsInitialized" title="PyEval_ThreadsInitialized"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_ThreadsInitialized()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ArithmeticError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_AssertionError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_AttributeError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_BaseException</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_BaseExceptionGroup</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_BlockingIOError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_BrokenPipeError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_BufferError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_BytesWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ChildProcessError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ConnectionAbortedError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ConnectionError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ConnectionRefusedError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ConnectionResetError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_DeprecationWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_EOFError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_EncodingWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_EnvironmentError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_Exception</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_FileExistsError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_FileNotFoundError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_FloatingPointError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_FutureWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_GeneratorExit</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_IOError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ImportError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ImportWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_IndentationError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_IndexError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_InterruptedError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_IsADirectoryError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_KeyError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_KeyboardInterrupt</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_LookupError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_MemoryError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ModuleNotFoundError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_NameError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_NotADirectoryError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_NotImplementedError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_OSError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_OverflowError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_PendingDeprecationWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_PermissionError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ProcessLookupError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_RecursionError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ReferenceError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ResourceWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_RuntimeError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_RuntimeWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_StopAsyncIteration</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_StopIteration</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_SyntaxError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_SyntaxWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_SystemError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_SystemExit</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_TabError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_TimeoutError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_TypeError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_UnboundLocalError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_UnicodeDecodeError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_UnicodeEncodeError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_UnicodeError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_UnicodeTranslateError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_UnicodeWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_UserWarning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ValueError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_Warning</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_WindowsError</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyExc_ZeroDivisionError</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyExceptionClass_Name()</span></code></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_GetArgs" title="PyException_GetArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_GetArgs()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_GetCause" title="PyException_GetCause"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_GetCause()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_GetContext" title="PyException_GetContext"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_GetContext()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_GetTraceback" title="PyException_GetTraceback"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_GetTraceback()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_SetArgs" title="PyException_SetArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_SetArgs()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_SetCause" title="PyException_SetCause"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_SetCause()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_SetContext" title="PyException_SetContext"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_SetContext()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyException_SetTraceback" title="PyException_SetTraceback"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyException_SetTraceback()</span></code></a></p></li>
<li><p><a class="reference internal" href="file.html#c.PyFile_FromFd" title="PyFile_FromFd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFile_FromFd()</span></code></a></p></li>
<li><p><a class="reference internal" href="file.html#c.PyFile_GetLine" title="PyFile_GetLine"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFile_GetLine()</span></code></a></p></li>
<li><p><a class="reference internal" href="file.html#c.PyFile_WriteObject" title="PyFile_WriteObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFile_WriteObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="file.html#c.PyFile_WriteString" title="PyFile_WriteString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFile_WriteString()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyFilter_Type</span></code></p></li>
<li><p><a class="reference internal" href="float.html#c.PyFloat_AsDouble" title="PyFloat_AsDouble"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFloat_AsDouble()</span></code></a></p></li>
<li><p><a class="reference internal" href="float.html#c.PyFloat_FromDouble" title="PyFloat_FromDouble"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFloat_FromDouble()</span></code></a></p></li>
<li><p><a class="reference internal" href="float.html#c.PyFloat_FromString" title="PyFloat_FromString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFloat_FromString()</span></code></a></p></li>
<li><p><a class="reference internal" href="float.html#c.PyFloat_GetInfo" title="PyFloat_GetInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFloat_GetInfo()</span></code></a></p></li>
<li><p><a class="reference internal" href="float.html#c.PyFloat_GetMax" title="PyFloat_GetMax"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFloat_GetMax()</span></code></a></p></li>
<li><p><a class="reference internal" href="float.html#c.PyFloat_GetMin" title="PyFloat_GetMin"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFloat_GetMin()</span></code></a></p></li>
<li><p><a class="reference internal" href="float.html#c.PyFloat_Type" title="PyFloat_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyFloat_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="frame.html#c.PyFrameObject" title="PyFrameObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyFrameObject</span></code></a></p></li>
<li><p><a class="reference internal" href="frame.html#c.PyFrame_GetCode" title="PyFrame_GetCode"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFrame_GetCode()</span></code></a></p></li>
<li><p><a class="reference internal" href="frame.html#c.PyFrame_GetLineNumber" title="PyFrame_GetLineNumber"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFrame_GetLineNumber()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PyFrozenSet_New" title="PyFrozenSet_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyFrozenSet_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PyFrozenSet_Type" title="PyFrozenSet_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyFrozenSet_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyGC_Collect" title="PyGC_Collect"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGC_Collect()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyGC_Disable" title="PyGC_Disable"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGC_Disable()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyGC_Enable" title="PyGC_Enable"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGC_Enable()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyGC_IsEnabled" title="PyGC_IsEnabled"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGC_IsEnabled()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyGILState_GetThisThreadState" title="PyGILState_GetThisThreadState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_GetThisThreadState()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">PyGILState_STATE</span></code></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyGetSetDef" title="PyGetSetDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyGetSetDef</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyGetSetDescr_Type</span></code></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_AddModule" title="PyImport_AddModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_AddModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_AddModuleObject" title="PyImport_AddModuleObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_AddModuleObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_AppendInittab" title="PyImport_AppendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_AppendInittab()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ExecCodeModule" title="PyImport_ExecCodeModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ExecCodeModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ExecCodeModuleEx" title="PyImport_ExecCodeModuleEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ExecCodeModuleEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ExecCodeModuleObject" title="PyImport_ExecCodeModuleObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ExecCodeModuleObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ExecCodeModuleWithPathnames" title="PyImport_ExecCodeModuleWithPathnames"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ExecCodeModuleWithPathnames()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_GetImporter" title="PyImport_GetImporter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_GetImporter()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_GetMagicNumber" title="PyImport_GetMagicNumber"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_GetMagicNumber()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_GetMagicTag" title="PyImport_GetMagicTag"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_GetMagicTag()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_GetModule" title="PyImport_GetModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_GetModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_GetModuleDict" title="PyImport_GetModuleDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_GetModuleDict()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_Import" title="PyImport_Import"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_Import()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ImportFrozenModule" title="PyImport_ImportFrozenModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ImportFrozenModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ImportFrozenModuleObject" title="PyImport_ImportFrozenModuleObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ImportFrozenModuleObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ImportModule" title="PyImport_ImportModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ImportModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ImportModuleLevel" title="PyImport_ImportModuleLevel"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ImportModuleLevel()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ImportModuleLevelObject" title="PyImport_ImportModuleLevelObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ImportModuleLevelObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ImportModuleNoBlock" title="PyImport_ImportModuleNoBlock"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ImportModuleNoBlock()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ReloadModule" title="PyImport_ReloadModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ReloadModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyIndex_Check" title="PyIndex_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyIndex_Check()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyInterpreterState" title="PyInterpreterState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyInterpreterState</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyInterpreterState_Clear" title="PyInterpreterState_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_Clear()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyInterpreterState_Delete" title="PyInterpreterState_Delete"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_Delete()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyInterpreterState_Get" title="PyInterpreterState_Get"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_Get()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyInterpreterState_GetDict" title="PyInterpreterState_GetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_GetDict()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyInterpreterState_GetID" title="PyInterpreterState_GetID"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_GetID()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyInterpreterState_New" title="PyInterpreterState_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="iter.html#c.PyIter_Check" title="PyIter_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyIter_Check()</span></code></a></p></li>
<li><p><a class="reference internal" href="iter.html#c.PyIter_Next" title="PyIter_Next"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyIter_Next()</span></code></a></p></li>
<li><p><a class="reference internal" href="iter.html#c.PyIter_Send" title="PyIter_Send"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyIter_Send()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyListIter_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyListRevIter_Type</span></code></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_Append" title="PyList_Append"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_Append()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_AsTuple" title="PyList_AsTuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_AsTuple()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_GetItem" title="PyList_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_GetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_GetSlice" title="PyList_GetSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_GetSlice()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_Insert" title="PyList_Insert"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_Insert()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_New" title="PyList_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_Reverse" title="PyList_Reverse"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_Reverse()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_SetItem" title="PyList_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_SetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_SetSlice" title="PyList_SetSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_SetSlice()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_Size" title="PyList_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_Sort" title="PyList_Sort"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_Sort()</span></code></a></p></li>
<li><p><a class="reference internal" href="list.html#c.PyList_Type" title="PyList_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyList_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLongObject" title="PyLongObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyLongObject</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyLongRangeIter_Type</span></code></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsDouble" title="PyLong_AsDouble"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsDouble()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsLong" title="PyLong_AsLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsLongAndOverflow" title="PyLong_AsLongAndOverflow"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsLongAndOverflow()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsLongLong" title="PyLong_AsLongLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsLongLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsLongLongAndOverflow" title="PyLong_AsLongLongAndOverflow"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsLongLongAndOverflow()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsSize_t" title="PyLong_AsSize_t"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsSize_t()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsSsize_t" title="PyLong_AsSsize_t"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsSsize_t()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsUnsignedLong" title="PyLong_AsUnsignedLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsUnsignedLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsUnsignedLongLong" title="PyLong_AsUnsignedLongLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsUnsignedLongLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsUnsignedLongLongMask" title="PyLong_AsUnsignedLongLongMask"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsUnsignedLongLongMask()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsUnsignedLongMask" title="PyLong_AsUnsignedLongMask"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsUnsignedLongMask()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_AsVoidPtr" title="PyLong_AsVoidPtr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_AsVoidPtr()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromDouble" title="PyLong_FromDouble"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromDouble()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromLong" title="PyLong_FromLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromLongLong" title="PyLong_FromLongLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromLongLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromSize_t" title="PyLong_FromSize_t"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromSize_t()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromSsize_t" title="PyLong_FromSsize_t"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromSsize_t()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromString" title="PyLong_FromString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromString()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromUnsignedLong" title="PyLong_FromUnsignedLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromUnsignedLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromUnsignedLongLong" title="PyLong_FromUnsignedLongLong"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromUnsignedLongLong()</span></code></a></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_FromVoidPtr" title="PyLong_FromVoidPtr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_FromVoidPtr()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyLong_GetInfo()</span></code></p></li>
<li><p><a class="reference internal" href="long.html#c.PyLong_Type" title="PyLong_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyLong_Type</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyMap_Type</span></code></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_Check" title="PyMapping_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Check()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_GetItemString" title="PyMapping_GetItemString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_GetItemString()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_HasKey" title="PyMapping_HasKey"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_HasKey()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_HasKeyString" title="PyMapping_HasKeyString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_HasKeyString()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_Items" title="PyMapping_Items"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Items()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_Keys" title="PyMapping_Keys"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Keys()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_Length" title="PyMapping_Length"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Length()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_SetItemString" title="PyMapping_SetItemString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_SetItemString()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_Size" title="PyMapping_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="mapping.html#c.PyMapping_Values" title="PyMapping_Values"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMapping_Values()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_Calloc" title="PyMem_Calloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_Calloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_Free" title="PyMem_Free"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_Free()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_Malloc" title="PyMem_Malloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_Malloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_Realloc" title="PyMem_Realloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_Realloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyMemberDef" title="PyMemberDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMemberDef</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyMemberDescr_Type</span></code></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyMember_GetOne" title="PyMember_GetOne"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMember_GetOne()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyMember_SetOne" title="PyMember_SetOne"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMember_SetOne()</span></code></a></p></li>
<li><p><a class="reference internal" href="memoryview.html#c.PyMemoryView_FromBuffer" title="PyMemoryView_FromBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMemoryView_FromBuffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="memoryview.html#c.PyMemoryView_FromMemory" title="PyMemoryView_FromMemory"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMemoryView_FromMemory()</span></code></a></p></li>
<li><p><a class="reference internal" href="memoryview.html#c.PyMemoryView_FromObject" title="PyMemoryView_FromObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMemoryView_FromObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="memoryview.html#c.PyMemoryView_GetContiguous" title="PyMemoryView_GetContiguous"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMemoryView_GetContiguous()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyMemoryView_Type</span></code></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyMethodDef" title="PyMethodDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyMethodDef</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyMethodDescr_Type</span></code></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModuleDef" title="PyModuleDef"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyModuleDef</span></code></a></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">PyModuleDef_Base</span></code></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModuleDef_Init" title="PyModuleDef_Init"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModuleDef_Init()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyModuleDef_Type</span></code></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_AddFunctions" title="PyModule_AddFunctions"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddFunctions()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_AddIntConstant" title="PyModule_AddIntConstant"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddIntConstant()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_AddObject" title="PyModule_AddObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_AddObjectRef" title="PyModule_AddObjectRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddObjectRef()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_AddStringConstant" title="PyModule_AddStringConstant"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddStringConstant()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_AddType" title="PyModule_AddType"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_AddType()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_Create2" title="PyModule_Create2"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_Create2()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_ExecDef" title="PyModule_ExecDef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_ExecDef()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_FromDefAndSpec2" title="PyModule_FromDefAndSpec2"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_FromDefAndSpec2()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_GetDef" title="PyModule_GetDef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetDef()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_GetDict" title="PyModule_GetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetDict()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_GetFilename" title="PyModule_GetFilename"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetFilename()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_GetFilenameObject" title="PyModule_GetFilenameObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetFilenameObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_GetName" title="PyModule_GetName"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetName()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_GetNameObject" title="PyModule_GetNameObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetNameObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_New" title="PyModule_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_NewObject" title="PyModule_NewObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_NewObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_SetDocString" title="PyModule_SetDocString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_SetDocString()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyModule_Type" title="PyModule_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyModule_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Absolute" title="PyNumber_Absolute"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Absolute()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Add" title="PyNumber_Add"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Add()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_And" title="PyNumber_And"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_And()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_AsSsize_t" title="PyNumber_AsSsize_t"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_AsSsize_t()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Check" title="PyNumber_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Check()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Divmod" title="PyNumber_Divmod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Divmod()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Float" title="PyNumber_Float"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Float()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_FloorDivide" title="PyNumber_FloorDivide"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_FloorDivide()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceAdd" title="PyNumber_InPlaceAdd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceAdd()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceAnd" title="PyNumber_InPlaceAnd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceAnd()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceFloorDivide" title="PyNumber_InPlaceFloorDivide"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceFloorDivide()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceLshift" title="PyNumber_InPlaceLshift"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceLshift()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceMatrixMultiply" title="PyNumber_InPlaceMatrixMultiply"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceMatrixMultiply()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceMultiply" title="PyNumber_InPlaceMultiply"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceMultiply()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceOr" title="PyNumber_InPlaceOr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceOr()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlacePower" title="PyNumber_InPlacePower"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlacePower()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceRemainder" title="PyNumber_InPlaceRemainder"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceRemainder()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceRshift" title="PyNumber_InPlaceRshift"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceRshift()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceSubtract" title="PyNumber_InPlaceSubtract"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceSubtract()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceTrueDivide" title="PyNumber_InPlaceTrueDivide"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceTrueDivide()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_InPlaceXor" title="PyNumber_InPlaceXor"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_InPlaceXor()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Index" title="PyNumber_Index"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Index()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Invert" title="PyNumber_Invert"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Invert()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Long" title="PyNumber_Long"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Long()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Lshift" title="PyNumber_Lshift"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Lshift()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_MatrixMultiply" title="PyNumber_MatrixMultiply"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_MatrixMultiply()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Multiply" title="PyNumber_Multiply"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Multiply()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Negative" title="PyNumber_Negative"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Negative()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Or" title="PyNumber_Or"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Or()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Positive" title="PyNumber_Positive"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Positive()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Power" title="PyNumber_Power"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Power()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Remainder" title="PyNumber_Remainder"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Remainder()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Rshift" title="PyNumber_Rshift"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Rshift()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Subtract" title="PyNumber_Subtract"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Subtract()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_ToBase" title="PyNumber_ToBase"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_ToBase()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_TrueDivide" title="PyNumber_TrueDivide"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_TrueDivide()</span></code></a></p></li>
<li><p><a class="reference internal" href="number.html#c.PyNumber_Xor" title="PyNumber_Xor"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyNumber_Xor()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_AfterFork" title="PyOS_AfterFork"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_AfterFork()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_AfterFork_Child" title="PyOS_AfterFork_Child"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_AfterFork_Child()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_AfterFork_Parent" title="PyOS_AfterFork_Parent"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_AfterFork_Parent()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_BeforeFork" title="PyOS_BeforeFork"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_BeforeFork()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_CheckStack" title="PyOS_CheckStack"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_CheckStack()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_FSPath" title="PyOS_FSPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_FSPath()</span></code></a></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.PyOS_InputHook" title="PyOS_InputHook"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyOS_InputHook</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_InterruptOccurred()</span></code></p></li>
<li><p><a class="reference internal" href="conversion.html#c.PyOS_double_to_string" title="PyOS_double_to_string"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_double_to_string()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_getsig" title="PyOS_getsig"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_getsig()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_mystricmp()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_mystrnicmp()</span></code></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_setsig" title="PyOS_setsig"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_setsig()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PyOS_sighandler_t" title="PyOS_sighandler_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyOS_sighandler_t</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#c.PyOS_snprintf" title="PyOS_snprintf"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_snprintf()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#c.PyOS_string_to_double" title="PyOS_string_to_double"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_string_to_double()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#c.PyOS_strtol" title="PyOS_strtol"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_strtol()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#c.PyOS_strtoul" title="PyOS_strtoul"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_strtoul()</span></code></a></p></li>
<li><p><a class="reference internal" href="conversion.html#c.PyOS_vsnprintf" title="PyOS_vsnprintf"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_vsnprintf()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.PyObject.ob_refcnt" title="PyObject.ob_refcnt"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyObject.ob_refcnt</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.PyObject.ob_type" title="PyObject.ob_type"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyObject.ob_type</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_ASCII" title="PyObject_ASCII"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_ASCII()</span></code></a></p></li>
<li><p><a class="reference internal" href="objbuffer.html#c.PyObject_AsCharBuffer" title="PyObject_AsCharBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_AsCharBuffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="file.html#c.PyObject_AsFileDescriptor" title="PyObject_AsFileDescriptor"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_AsFileDescriptor()</span></code></a></p></li>
<li><p><a class="reference internal" href="objbuffer.html#c.PyObject_AsReadBuffer" title="PyObject_AsReadBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_AsReadBuffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="objbuffer.html#c.PyObject_AsWriteBuffer" title="PyObject_AsWriteBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_AsWriteBuffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Bytes" title="PyObject_Bytes"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Bytes()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_Call" title="PyObject_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Call()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_CallFunction" title="PyObject_CallFunction"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallFunction()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_CallFunctionObjArgs" title="PyObject_CallFunctionObjArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallFunctionObjArgs()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_CallMethod" title="PyObject_CallMethod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallMethod()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_CallMethodObjArgs" title="PyObject_CallMethodObjArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallMethodObjArgs()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_CallNoArgs" title="PyObject_CallNoArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallNoArgs()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_CallObject" title="PyObject_CallObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CallObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyObject_Calloc" title="PyObject_Calloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Calloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyObject_CheckBuffer" title="PyObject_CheckBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CheckBuffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="objbuffer.html#c.PyObject_CheckReadBuffer" title="PyObject_CheckReadBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CheckReadBuffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="weakref.html#c.PyObject_ClearWeakRefs" title="PyObject_ClearWeakRefs"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_ClearWeakRefs()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyObject_CopyData" title="PyObject_CopyData"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_CopyData()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_DelItem" title="PyObject_DelItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_DelItem()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_DelItemString()</span></code></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Dir" title="PyObject_Dir"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Dir()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Format" title="PyObject_Format"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Format()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyObject_Free" title="PyObject_Free"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Free()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyObject_GC_Del" title="PyObject_GC_Del"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_Del()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyObject_GC_IsFinalized" title="PyObject_GC_IsFinalized"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_IsFinalized()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyObject_GC_IsTracked" title="PyObject_GC_IsTracked"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_IsTracked()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyObject_GC_Track" title="PyObject_GC_Track"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_Track()</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.PyObject_GC_UnTrack" title="PyObject_GC_UnTrack"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GC_UnTrack()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GenericGetAttr" title="PyObject_GenericGetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericGetAttr()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GenericGetDict" title="PyObject_GenericGetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericGetDict()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GenericSetAttr" title="PyObject_GenericSetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericSetAttr()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GenericSetDict" title="PyObject_GenericSetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GenericSetDict()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GetAIter" title="PyObject_GetAIter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetAIter()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GetAttr" title="PyObject_GetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetAttr()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GetAttrString" title="PyObject_GetAttrString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetAttrString()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GetItem" title="PyObject_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GetIter" title="PyObject_GetIter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetIter()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_GetTypeData" title="PyObject_GetTypeData"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetTypeData()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_HasAttr" title="PyObject_HasAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_HasAttr()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_HasAttrString" title="PyObject_HasAttrString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_HasAttrString()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Hash" title="PyObject_Hash"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Hash()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_HashNotImplemented" title="PyObject_HashNotImplemented"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_HashNotImplemented()</span></code></a></p></li>
<li><p><a class="reference internal" href="allocation.html#c.PyObject_Init" title="PyObject_Init"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Init()</span></code></a></p></li>
<li><p><a class="reference internal" href="allocation.html#c.PyObject_InitVar" title="PyObject_InitVar"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_InitVar()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_IsInstance" title="PyObject_IsInstance"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_IsInstance()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_IsSubclass" title="PyObject_IsSubclass"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_IsSubclass()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_IsTrue" title="PyObject_IsTrue"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_IsTrue()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Length" title="PyObject_Length"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Length()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyObject_Malloc" title="PyObject_Malloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Malloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Not" title="PyObject_Not"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Not()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyObject_Realloc" title="PyObject_Realloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Realloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Repr" title="PyObject_Repr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Repr()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_RichCompare" title="PyObject_RichCompare"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_RichCompare()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_RichCompareBool" title="PyObject_RichCompareBool"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_RichCompareBool()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SelfIter()</span></code></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_SetAttr" title="PyObject_SetAttr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetAttr()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_SetAttrString" title="PyObject_SetAttrString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetAttrString()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_SetItem" title="PyObject_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Size" title="PyObject_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Str" title="PyObject_Str"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Str()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyObject_Type" title="PyObject_Type"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Type()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_Vectorcall" title="PyObject_Vectorcall"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_Vectorcall()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyObject_VectorcallMethod" title="PyObject_VectorcallMethod"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_VectorcallMethod()</span></code></a></p></li>
<li><p><a class="reference internal" href="descriptor.html#c.PyProperty_Type" title="PyProperty_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyProperty_Type</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyRangeIter_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyRange_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyReversed_Type</span></code></p></li>
<li><p><a class="reference internal" href="iterator.html#c.PySeqIter_New" title="PySeqIter_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySeqIter_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="iterator.html#c.PySeqIter_Type" title="PySeqIter_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PySeqIter_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Check" title="PySequence_Check"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Check()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Concat" title="PySequence_Concat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Concat()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Contains" title="PySequence_Contains"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Contains()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Count" title="PySequence_Count"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Count()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_DelItem" title="PySequence_DelItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_DelItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_DelSlice" title="PySequence_DelSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_DelSlice()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Fast" title="PySequence_Fast"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Fast()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_GetItem" title="PySequence_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_GetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_GetSlice" title="PySequence_GetSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_GetSlice()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_In()</span></code></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_InPlaceConcat" title="PySequence_InPlaceConcat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_InPlaceConcat()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_InPlaceRepeat" title="PySequence_InPlaceRepeat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_InPlaceRepeat()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Index" title="PySequence_Index"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Index()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Length" title="PySequence_Length"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Length()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_List" title="PySequence_List"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_List()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Repeat" title="PySequence_Repeat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Repeat()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_SetItem" title="PySequence_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_SetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_SetSlice" title="PySequence_SetSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_SetSlice()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Size" title="PySequence_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="sequence.html#c.PySequence_Tuple" title="PySequence_Tuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySequence_Tuple()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PySetIter_Type</span></code></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_Add" title="PySet_Add"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySet_Add()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_Clear" title="PySet_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySet_Clear()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_Contains" title="PySet_Contains"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySet_Contains()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_Discard" title="PySet_Discard"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySet_Discard()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_New" title="PySet_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySet_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_Pop" title="PySet_Pop"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySet_Pop()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_Size" title="PySet_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySet_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="set.html#c.PySet_Type" title="PySet_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PySet_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="slice.html#c.PySlice_AdjustIndices" title="PySlice_AdjustIndices"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_AdjustIndices()</span></code></a></p></li>
<li><p><a class="reference internal" href="slice.html#c.PySlice_GetIndices" title="PySlice_GetIndices"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_GetIndices()</span></code></a></p></li>
<li><p><a class="reference internal" href="slice.html#c.PySlice_GetIndicesEx" title="PySlice_GetIndicesEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_GetIndicesEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="slice.html#c.PySlice_New" title="PySlice_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="slice.html#c.PySlice_Type" title="PySlice_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PySlice_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="slice.html#c.PySlice_Unpack" title="PySlice_Unpack"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySlice_Unpack()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyState_AddModule" title="PyState_AddModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyState_AddModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyState_FindModule" title="PyState_FindModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyState_FindModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="module.html#c.PyState_RemoveModule" title="PyState_RemoveModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyState_RemoveModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyStructSequence_Desc" title="PyStructSequence_Desc"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyStructSequence_Desc</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyStructSequence_Field" title="PyStructSequence_Field"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyStructSequence_Field</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyStructSequence_GetItem" title="PyStructSequence_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyStructSequence_GetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyStructSequence_New" title="PyStructSequence_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyStructSequence_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyStructSequence_NewType" title="PyStructSequence_NewType"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyStructSequence_NewType()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyStructSequence_SetItem" title="PyStructSequence_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyStructSequence_SetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyStructSequence_UnnamedField" title="PyStructSequence_UnnamedField"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyStructSequence_UnnamedField</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PySuper_Type</span></code></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_AddWarnOption" title="PySys_AddWarnOption"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddWarnOption()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_AddWarnOptionUnicode" title="PySys_AddWarnOptionUnicode"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddWarnOptionUnicode()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_AddXOption" title="PySys_AddXOption"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddXOption()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_FormatStderr" title="PySys_FormatStderr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_FormatStderr()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_FormatStdout" title="PySys_FormatStdout"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_FormatStdout()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_GetObject" title="PySys_GetObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_GetObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_GetXOptions" title="PySys_GetXOptions"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_GetXOptions()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_HasWarnOptions()</span></code></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_ResetWarnOptions" title="PySys_ResetWarnOptions"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_ResetWarnOptions()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PySys_SetArgv" title="PySys_SetArgv"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_SetArgv()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PySys_SetArgvEx" title="PySys_SetArgvEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_SetArgvEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_SetObject" title="PySys_SetObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_SetObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_SetPath" title="PySys_SetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_SetPath()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_WriteStderr" title="PySys_WriteStderr"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_WriteStderr()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_WriteStdout" title="PySys_WriteStdout"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_WriteStdout()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState" title="PyThreadState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyThreadState</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_Clear" title="PyThreadState_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Clear()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_Delete" title="PyThreadState_Delete"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Delete()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_Get" title="PyThreadState_Get"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Get()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_GetDict" title="PyThreadState_GetDict"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_GetDict()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_GetFrame" title="PyThreadState_GetFrame"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_GetFrame()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_GetID" title="PyThreadState_GetID"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_GetID()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_GetInterpreter" title="PyThreadState_GetInterpreter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_GetInterpreter()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_New" title="PyThreadState_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_SetAsyncExc" title="PyThreadState_SetAsyncExc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_SetAsyncExc()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThreadState_Swap" title="PyThreadState_Swap"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Swap()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_GetInfo()</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_ReInitTLS" title="PyThread_ReInitTLS"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_ReInitTLS()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_acquire_lock()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_acquire_lock_timed()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_allocate_lock()</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_create_key" title="PyThread_create_key"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_create_key()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_delete_key" title="PyThread_delete_key"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_delete_key()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_delete_key_value" title="PyThread_delete_key_value"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_delete_key_value()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_exit_thread()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_free_lock()</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_get_key_value" title="PyThread_get_key_value"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_get_key_value()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_get_stacksize()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_get_thread_ident()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_get_thread_native_id()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_init_thread()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_release_lock()</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_set_key_value" title="PyThread_set_key_value"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_set_key_value()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_set_stacksize()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_start_new_thread()</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_tss_alloc" title="PyThread_tss_alloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_alloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_tss_create" title="PyThread_tss_create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_create()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_tss_delete" title="PyThread_tss_delete"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_delete()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_tss_free" title="PyThread_tss_free"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_free()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_tss_get" title="PyThread_tss_get"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_get()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_tss_is_created" title="PyThread_tss_is_created"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_is_created()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.PyThread_tss_set" title="PyThread_tss_set"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_set()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTraceBack_Here()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTraceBack_Print()</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTraceBack_Type</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTupleIter_Type</span></code></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyTuple_GetItem" title="PyTuple_GetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTuple_GetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyTuple_GetSlice" title="PyTuple_GetSlice"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTuple_GetSlice()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyTuple_New" title="PyTuple_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTuple_New()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyTuple_Pack" title="PyTuple_Pack"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTuple_Pack()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyTuple_SetItem" title="PyTuple_SetItem"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTuple_SetItem()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyTuple_Size" title="PyTuple_Size"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyTuple_Size()</span></code></a></p></li>
<li><p><a class="reference internal" href="tuple.html#c.PyTuple_Type" title="PyTuple_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTuple_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_ClearCache" title="PyType_ClearCache"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_ClearCache()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_FromMetaclass" title="PyType_FromMetaclass"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromMetaclass()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_FromModuleAndSpec" title="PyType_FromModuleAndSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromModuleAndSpec()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_FromSpec" title="PyType_FromSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpec()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_FromSpecWithBases" title="PyType_FromSpecWithBases"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_FromSpecWithBases()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GenericAlloc" title="PyType_GenericAlloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GenericAlloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GenericNew" title="PyType_GenericNew"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GenericNew()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GetFlags" title="PyType_GetFlags"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetFlags()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GetModule" title="PyType_GetModule"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetModule()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GetModuleState" title="PyType_GetModuleState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetModuleState()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GetName" title="PyType_GetName"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetName()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GetQualName" title="PyType_GetQualName"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetQualName()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_GetSlot" title="PyType_GetSlot"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetSlot()</span></code></a></p></li>
<li><p><a class="reference internal" href="object.html#c.PyType_GetTypeDataSize" title="PyType_GetTypeDataSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_GetTypeDataSize()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_IsSubtype" title="PyType_IsSubtype"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_IsSubtype()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_Modified" title="PyType_Modified"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Modified()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_Ready" title="PyType_Ready"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyType_Ready()</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_Slot" title="PyType_Slot"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyType_Slot</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_Spec" title="PyType_Spec"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyType_Spec</span></code></a></p></li>
<li><p><a class="reference internal" href="type.html#c.PyType_Type" title="PyType_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyType_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_Create" title="PyUnicodeDecodeError_Create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_Create()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_GetEncoding" title="PyUnicodeDecodeError_GetEncoding"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_GetEncoding()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_GetEnd" title="PyUnicodeDecodeError_GetEnd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_GetEnd()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_GetObject" title="PyUnicodeDecodeError_GetObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_GetObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_GetReason" title="PyUnicodeDecodeError_GetReason"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_GetReason()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_GetStart" title="PyUnicodeDecodeError_GetStart"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_GetStart()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_SetEnd" title="PyUnicodeDecodeError_SetEnd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_SetEnd()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_SetReason" title="PyUnicodeDecodeError_SetReason"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_SetReason()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeDecodeError_SetStart" title="PyUnicodeDecodeError_SetStart"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeDecodeError_SetStart()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_GetEncoding" title="PyUnicodeEncodeError_GetEncoding"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_GetEncoding()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_GetEnd" title="PyUnicodeEncodeError_GetEnd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_GetEnd()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_GetObject" title="PyUnicodeEncodeError_GetObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_GetObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_GetReason" title="PyUnicodeEncodeError_GetReason"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_GetReason()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_GetStart" title="PyUnicodeEncodeError_GetStart"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_GetStart()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_SetEnd" title="PyUnicodeEncodeError_SetEnd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_SetEnd()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_SetReason" title="PyUnicodeEncodeError_SetReason"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_SetReason()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeEncodeError_SetStart" title="PyUnicodeEncodeError_SetStart"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeEncodeError_SetStart()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyUnicodeIter_Type</span></code></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeTranslateError_GetEnd" title="PyUnicodeTranslateError_GetEnd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeTranslateError_GetEnd()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeTranslateError_GetObject" title="PyUnicodeTranslateError_GetObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeTranslateError_GetObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeTranslateError_GetReason" title="PyUnicodeTranslateError_GetReason"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeTranslateError_GetReason()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeTranslateError_GetStart" title="PyUnicodeTranslateError_GetStart"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeTranslateError_GetStart()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeTranslateError_SetEnd" title="PyUnicodeTranslateError_SetEnd"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeTranslateError_SetEnd()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeTranslateError_SetReason" title="PyUnicodeTranslateError_SetReason"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeTranslateError_SetReason()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.PyUnicodeTranslateError_SetStart" title="PyUnicodeTranslateError_SetStart"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicodeTranslateError_SetStart()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Append()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AppendAndDel()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsASCIIString" title="PyUnicode_AsASCIIString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsASCIIString()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsCharmapString" title="PyUnicode_AsCharmapString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsCharmapString()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsDecodedObject()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsDecodedUnicode()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsEncodedObject()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsEncodedString" title="PyUnicode_AsEncodedString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsEncodedString()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsEncodedUnicode()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsLatin1String" title="PyUnicode_AsLatin1String"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsLatin1String()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsMBCSString" title="PyUnicode_AsMBCSString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsMBCSString()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsRawUnicodeEscapeString" title="PyUnicode_AsRawUnicodeEscapeString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsRawUnicodeEscapeString()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsUCS4" title="PyUnicode_AsUCS4"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsUCS4()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsUCS4Copy" title="PyUnicode_AsUCS4Copy"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsUCS4Copy()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsUTF16String" title="PyUnicode_AsUTF16String"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsUTF16String()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsUTF32String" title="PyUnicode_AsUTF32String"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsUTF32String()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsUTF8AndSize" title="PyUnicode_AsUTF8AndSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsUTF8AndSize()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsUTF8String" title="PyUnicode_AsUTF8String"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsUTF8String()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsUnicodeEscapeString" title="PyUnicode_AsUnicodeEscapeString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsUnicodeEscapeString()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsWideChar" title="PyUnicode_AsWideChar"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsWideChar()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_AsWideCharString" title="PyUnicode_AsWideCharString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_AsWideCharString()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_BuildEncodingMap()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Compare" title="PyUnicode_Compare"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Compare()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_CompareWithASCIIString" title="PyUnicode_CompareWithASCIIString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_CompareWithASCIIString()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Concat" title="PyUnicode_Concat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Concat()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Contains" title="PyUnicode_Contains"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Contains()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Count" title="PyUnicode_Count"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Count()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Decode" title="PyUnicode_Decode"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Decode()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeASCII" title="PyUnicode_DecodeASCII"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeASCII()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeCharmap" title="PyUnicode_DecodeCharmap"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeCharmap()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeCodePageStateful()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeFSDefault" title="PyUnicode_DecodeFSDefault"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeFSDefault()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeFSDefaultAndSize" title="PyUnicode_DecodeFSDefaultAndSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeFSDefaultAndSize()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeLatin1" title="PyUnicode_DecodeLatin1"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeLatin1()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeLocale" title="PyUnicode_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeLocale()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeLocaleAndSize" title="PyUnicode_DecodeLocaleAndSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeLocaleAndSize()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeMBCS" title="PyUnicode_DecodeMBCS"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeMBCS()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeMBCSStateful" title="PyUnicode_DecodeMBCSStateful"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeMBCSStateful()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeRawUnicodeEscape" title="PyUnicode_DecodeRawUnicodeEscape"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeRawUnicodeEscape()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF16" title="PyUnicode_DecodeUTF16"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF16()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF16Stateful" title="PyUnicode_DecodeUTF16Stateful"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF16Stateful()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF32" title="PyUnicode_DecodeUTF32"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF32()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF32Stateful" title="PyUnicode_DecodeUTF32Stateful"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF32Stateful()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF7" title="PyUnicode_DecodeUTF7"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF7()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF7Stateful" title="PyUnicode_DecodeUTF7Stateful"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF7Stateful()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF8" title="PyUnicode_DecodeUTF8"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF8()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUTF8Stateful" title="PyUnicode_DecodeUTF8Stateful"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUTF8Stateful()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_DecodeUnicodeEscape" title="PyUnicode_DecodeUnicodeEscape"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_DecodeUnicodeEscape()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_EncodeCodePage" title="PyUnicode_EncodeCodePage"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_EncodeCodePage()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_EncodeFSDefault" title="PyUnicode_EncodeFSDefault"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_EncodeFSDefault()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_EncodeLocale" title="PyUnicode_EncodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_EncodeLocale()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FSConverter" title="PyUnicode_FSConverter"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FSConverter()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FSDecoder" title="PyUnicode_FSDecoder"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FSDecoder()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Find" title="PyUnicode_Find"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Find()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FindChar" title="PyUnicode_FindChar"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FindChar()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Format" title="PyUnicode_Format"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Format()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FromEncodedObject" title="PyUnicode_FromEncodedObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromEncodedObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FromFormat" title="PyUnicode_FromFormat"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromFormat()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FromFormatV" title="PyUnicode_FromFormatV"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromFormatV()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FromObject" title="PyUnicode_FromObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromObject()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromOrdinal()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FromString" title="PyUnicode_FromString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromString()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FromStringAndSize" title="PyUnicode_FromStringAndSize"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromStringAndSize()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_FromWideChar" title="PyUnicode_FromWideChar"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_FromWideChar()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_GetDefaultEncoding()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_GetLength" title="PyUnicode_GetLength"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_GetLength()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_InternFromString" title="PyUnicode_InternFromString"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_InternFromString()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_InternInPlace" title="PyUnicode_InternInPlace"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_InternInPlace()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_IsIdentifier" title="PyUnicode_IsIdentifier"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_IsIdentifier()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Join" title="PyUnicode_Join"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Join()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Partition()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_RPartition()</span></code></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_RSplit()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_ReadChar" title="PyUnicode_ReadChar"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_ReadChar()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Replace" title="PyUnicode_Replace"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Replace()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Resize()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_RichCompare" title="PyUnicode_RichCompare"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_RichCompare()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Split" title="PyUnicode_Split"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Split()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Splitlines" title="PyUnicode_Splitlines"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Splitlines()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Substring" title="PyUnicode_Substring"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Substring()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Tailmatch" title="PyUnicode_Tailmatch"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Tailmatch()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Translate" title="PyUnicode_Translate"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_Translate()</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_Type" title="PyUnicode_Type"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyUnicode_Type</span></code></a></p></li>
<li><p><a class="reference internal" href="unicode.html#c.PyUnicode_WriteChar" title="PyUnicode_WriteChar"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyUnicode_WriteChar()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.PyVarObject" title="PyVarObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyVarObject</span></code></a></p></li>
<li><p><code class="xref c c-member docutils literal notranslate"><span class="pre">PyVarObject.ob_base</span></code></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.PyVarObject.ob_size" title="PyVarObject.ob_size"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyVarObject.ob_size</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyVectorcall_Call" title="PyVectorcall_Call"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyVectorcall_Call()</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.PyVectorcall_NARGS" title="PyVectorcall_NARGS"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyVectorcall_NARGS()</span></code></a></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">PyWeakReference</span></code></p></li>
<li><p><a class="reference internal" href="weakref.html#c.PyWeakref_GetObject" title="PyWeakref_GetObject"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyWeakref_GetObject()</span></code></a></p></li>
<li><p><a class="reference internal" href="weakref.html#c.PyWeakref_NewProxy" title="PyWeakref_NewProxy"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyWeakref_NewProxy()</span></code></a></p></li>
<li><p><a class="reference internal" href="weakref.html#c.PyWeakref_NewRef" title="PyWeakref_NewRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyWeakref_NewRef()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyWrapperDescr_Type</span></code></p></li>
<li><p><a class="reference internal" href="descriptor.html#c.PyWrapper_New" title="PyWrapper_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyWrapper_New()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">PyZip_Type</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_AddPendingCall" title="Py_AddPendingCall"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_AddPendingCall()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.Py_AtExit" title="Py_AtExit"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_AtExit()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_BEGIN_ALLOW_THREADS" title="Py_BEGIN_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_BEGIN_ALLOW_THREADS</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_BLOCK_THREADS" title="Py_BLOCK_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_BLOCK_THREADS</span></code></a></p></li>
<li><p><a class="reference internal" href="arg.html#c.Py_BuildValue" title="Py_BuildValue"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_BuildValue()</span></code></a></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.Py_BytesMain" title="Py_BytesMain"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_BytesMain()</span></code></a></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.Py_CompileString" title="Py_CompileString"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_CompileString()</span></code></a></p></li>
<li><p><a class="reference internal" href="refcounting.html#c.Py_DecRef" title="Py_DecRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecRef()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_END_ALLOW_THREADS" title="Py_END_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_END_ALLOW_THREADS</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.Py_EncodeLocale" title="Py_EncodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_EncodeLocale()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_EndInterpreter" title="Py_EndInterpreter"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_EndInterpreter()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.Py_EnterRecursiveCall" title="Py_EnterRecursiveCall"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_EnterRecursiveCall()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.Py_Exit" title="Py_Exit"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Exit()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.Py_FatalError" title="Py_FatalError"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FatalError()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_FileSystemDefaultEncodeErrors</span></code></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_FileSystemDefaultEncoding</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_Finalize" title="Py_Finalize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Finalize()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="typehints.html#c.Py_GenericAlias" title="Py_GenericAlias"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GenericAlias()</span></code></a></p></li>
<li><p><a class="reference internal" href="typehints.html#c.Py_GenericAliasType" title="Py_GenericAliasType"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_GenericAliasType</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetBuildInfo" title="Py_GetBuildInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetBuildInfo()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetCompiler" title="Py_GetCompiler"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetCompiler()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetCopyright" title="Py_GetCopyright"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetCopyright()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetExecPrefix" title="Py_GetExecPrefix"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetExecPrefix()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetPath" title="Py_GetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPath()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetPlatform" title="Py_GetPlatform"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPlatform()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetPrefix" title="Py_GetPrefix"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPrefix()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetProgramFullPath" title="Py_GetProgramFullPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetProgramFullPath()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetProgramName" title="Py_GetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetProgramName()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetPythonHome" title="Py_GetPythonHome"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPythonHome()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetRecursionLimit()</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_GetVersion" title="Py_GetVersion"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetVersion()</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_HasFileSystemDefaultEncoding</span></code></p></li>
<li><p><a class="reference internal" href="refcounting.html#c.Py_IncRef" title="Py_IncRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_IncRef()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_InitializeEx" title="Py_InitializeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeEx()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.Py_Is" title="Py_Is"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Is()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.Py_IsFalse" title="Py_IsFalse"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_IsFalse()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_IsInitialized" title="Py_IsInitialized"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_IsInitialized()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.Py_IsNone" title="Py_IsNone"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_IsNone()</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.Py_IsTrue" title="Py_IsTrue"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_IsTrue()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.Py_LeaveRecursiveCall" title="Py_LeaveRecursiveCall"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_LeaveRecursiveCall()</span></code></a></p></li>
<li><p><a class="reference internal" href="veryhigh.html#c.Py_Main" title="Py_Main"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Main()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_MakePendingCalls()</span></code></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_NewInterpreter" title="Py_NewInterpreter"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_NewInterpreter()</span></code></a></p></li>
<li><p><a class="reference internal" href="refcounting.html#c.Py_NewRef" title="Py_NewRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_NewRef()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.Py_ReprEnter" title="Py_ReprEnter"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_ReprEnter()</span></code></a></p></li>
<li><p><a class="reference internal" href="exceptions.html#c.Py_ReprLeave" title="Py_ReprLeave"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_ReprLeave()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_SetPath" title="Py_SetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetPath()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_SetProgramName" title="Py_SetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_SetPythonHome" title="Py_SetPythonHome"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetPythonHome()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetRecursionLimit()</span></code></p></li>
<li><p><a class="reference internal" href="unicode.html#c.Py_UCS4" title="Py_UCS4"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_UCS4</span></code></a></p></li>
<li><p><a class="reference internal" href="init.html#c.Py_UNBLOCK_THREADS" title="Py_UNBLOCK_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_UNBLOCK_THREADS</span></code></a></p></li>
<li><p><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_UTF8Mode</span></code></p></li>
<li><p><a class="reference internal" href="arg.html#c.Py_VaBuildValue" title="Py_VaBuildValue"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_VaBuildValue()</span></code></a></p></li>
<li><p><a class="reference internal" href="apiabiversion.html#c.Py_Version" title="Py_Version"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_Version</span></code></a></p></li>
<li><p><a class="reference internal" href="refcounting.html#c.Py_XNewRef" title="Py_XNewRef"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_XNewRef()</span></code></a></p></li>
<li><p><a class="reference internal" href="buffer.html#c.Py_buffer" title="Py_buffer"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_buffer</span></code></a></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_intptr_t</span></code></p></li>
<li><p><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_uintptr_t</span></code></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.allocfunc" title="allocfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">allocfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.binaryfunc" title="binaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">binaryfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.descrgetfunc" title="descrgetfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">descrgetfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.descrsetfunc" title="descrsetfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">descrsetfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.destructor" title="destructor"><code class="xref c c-type docutils literal notranslate"><span class="pre">destructor</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.getattrfunc" title="getattrfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getattrfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.getattrofunc" title="getattrofunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getattrofunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.getbufferproc" title="getbufferproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getbufferproc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.getiterfunc" title="getiterfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">getiterfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.getter" title="getter"><code class="xref c c-type docutils literal notranslate"><span class="pre">getter</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.hashfunc" title="hashfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">hashfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.initproc" title="initproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">initproc</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.inquiry" title="inquiry"><code class="xref c c-type docutils literal notranslate"><span class="pre">inquiry</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.iternextfunc" title="iternextfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">iternextfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.lenfunc" title="lenfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">lenfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.newfunc" title="newfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">newfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.objobjargproc" title="objobjargproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">objobjargproc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.objobjproc" title="objobjproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">objobjproc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.releasebufferproc" title="releasebufferproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">releasebufferproc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.reprfunc" title="reprfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">reprfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.richcmpfunc" title="richcmpfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">richcmpfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.setattrfunc" title="setattrfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">setattrfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.setattrofunc" title="setattrofunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">setattrofunc</span></code></a></p></li>
<li><p><a class="reference internal" href="structures.html#c.setter" title="setter"><code class="xref c c-type docutils literal notranslate"><span class="pre">setter</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.ssizeargfunc" title="ssizeargfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeargfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.ssizeobjargproc" title="ssizeobjargproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizeobjargproc</span></code></a></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizessizeargfunc</span></code></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">ssizessizeobjargproc</span></code></p></li>
<li><p><code class="xref c c-type docutils literal notranslate"><span class="pre">symtable</span></code></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.ternaryfunc" title="ternaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">ternaryfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.traverseproc" title="traverseproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">traverseproc</span></code></a></p></li>
<li><p><a class="reference internal" href="typeobj.html#c.unaryfunc" title="unaryfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">unaryfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="call.html#c.vectorcallfunc" title="vectorcallfunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">vectorcallfunc</span></code></a></p></li>
<li><p><a class="reference internal" href="gcsupport.html#c.visitproc" title="visitproc"><code class="xref c c-type docutils literal notranslate"><span class="pre">visitproc</span></code></a></p></li>
</ul>
</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">C API Stability</a><ul>
<li><a class="reference internal" href="#unstable-c-api">Unstable C API</a></li>
<li><a class="reference internal" href="#stable-application-binary-interface">Stable Application Binary Interface</a><ul>
<li><a class="reference internal" href="#limited-c-api">Limited C API</a></li>
<li><a class="reference internal" href="#stable-abi">Stable ABI</a></li>
<li><a class="reference internal" href="#limited-api-scope-and-performance">Limited API Scope and Performance</a></li>
<li><a class="reference internal" href="#limited-api-caveats">Limited API Caveats</a></li>
</ul>
</li>
<li><a class="reference internal" href="#platform-considerations">Platform Considerations</a></li>
<li><a class="reference internal" href="#contents-of-limited-api">Contents of Limited API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="intro.html"
                          title="previous chapter">Introduction</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="veryhigh.html"
                          title="next chapter">The Very High Level Layer</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/stable.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="veryhigh.html" title="The Very High Level Layer"
             >next</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">C API Stability</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>