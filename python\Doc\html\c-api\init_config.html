<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Python Initialization Configuration" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/init_config.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Python can be initialized with Py_InitializeFromConfig() and the PyConfig structure. It can be preinitialized with Py_PreInitialize() and the PyPreConfig structure. There are two kinds of configura..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Python can be initialized with Py_InitializeFromConfig() and the PyConfig structure. It can be preinitialized with Py_PreInitialize() and the PyPreConfig structure. There are two kinds of configura..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Python Initialization Configuration &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Memory Management" href="memory.html" />
    <link rel="prev" title="Initialization, Finalization, and Threads" href="init.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/init_config.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Python Initialization Configuration</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#pywidestringlist">PyWideStringList</a></li>
<li><a class="reference internal" href="#pystatus">PyStatus</a></li>
<li><a class="reference internal" href="#pypreconfig">PyPreConfig</a></li>
<li><a class="reference internal" href="#preinitialize-python-with-pypreconfig">Preinitialize Python with PyPreConfig</a></li>
<li><a class="reference internal" href="#pyconfig">PyConfig</a></li>
<li><a class="reference internal" href="#initialization-with-pyconfig">Initialization with PyConfig</a></li>
<li><a class="reference internal" href="#isolated-configuration">Isolated Configuration</a></li>
<li><a class="reference internal" href="#python-configuration">Python Configuration</a></li>
<li><a class="reference internal" href="#python-path-configuration">Python Path Configuration</a></li>
<li><a class="reference internal" href="#py-runmain">Py_RunMain()</a></li>
<li><a class="reference internal" href="#py-getargcargv">Py_GetArgcArgv()</a></li>
<li><a class="reference internal" href="#multi-phase-initialization-private-provisional-api">Multi-Phase Initialization Private Provisional API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="init.html"
                          title="previous chapter">Initialization, Finalization, and Threads</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="memory.html"
                          title="next chapter">Memory Management</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/init_config.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="memory.html" title="Memory Management"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="init.html" title="Initialization, Finalization, and Threads"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Initialization Configuration</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="python-initialization-configuration">
<span id="init-config"></span><h1>Python Initialization Configuration<a class="headerlink" href="#python-initialization-configuration" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Python can be initialized with <a class="reference internal" href="#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> and the
<a class="reference internal" href="#c.PyConfig" title="PyConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyConfig</span></code></a> structure. It can be preinitialized with
<a class="reference internal" href="#c.Py_PreInitialize" title="Py_PreInitialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_PreInitialize()</span></code></a> and the <a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyPreConfig</span></code></a> structure.</p>
<p>There are two kinds of configuration:</p>
<ul class="simple">
<li><p>The <a class="reference internal" href="#init-python-config"><span class="std std-ref">Python Configuration</span></a> can be used to build a
customized Python which behaves as the regular Python. For example,
environment variables and command line arguments are used to configure
Python.</p></li>
<li><p>The <a class="reference internal" href="#init-isolated-conf"><span class="std std-ref">Isolated Configuration</span></a> can be used to embed
Python into an application. It isolates Python from the system. For example,
environment variables are ignored, the LC_CTYPE locale is left unchanged and
no signal handler is registered.</p></li>
</ul>
<p>The <a class="reference internal" href="#c.Py_RunMain" title="Py_RunMain"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_RunMain()</span></code></a> function can be used to write a customized Python
program.</p>
<p>See also <a class="reference internal" href="init.html#initialization"><span class="std std-ref">Initialization, Finalization, and Threads</span></a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0587/"><strong>PEP 587</strong></a> “Python Initialization Configuration”.</p>
</div>
<section id="example">
<h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>Example of customized Python always running in isolated mode:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">int</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">argc</span><span class="p">,</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">**</span><span class="n">argv</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyStatus</span><span class="w"> </span><span class="n">status</span><span class="p">;</span>

<span class="w">    </span><span class="n">PyConfig</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyConfig_InitPythonConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="n">config</span><span class="p">.</span><span class="n">isolated</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* Decode command line arguments.</span>
<span class="cm">       Implicitly preinitialize Python (in isolated mode). */</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyConfig_SetBytesArgv</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">,</span><span class="w"> </span><span class="n">argc</span><span class="p">,</span><span class="w"> </span><span class="n">argv</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">exception</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_InitializeFromConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">exception</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="n">PyConfig_Clear</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">Py_RunMain</span><span class="p">();</span>

<span class="nl">exception</span><span class="p">:</span>
<span class="w">    </span><span class="n">PyConfig_Clear</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_IsExit</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">status</span><span class="p">.</span><span class="n">exitcode</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="cm">/* Display the error message and exit the process with</span>
<span class="cm">       non-zero exit code */</span>
<span class="w">    </span><span class="n">Py_ExitStatusException</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="pywidestringlist">
<h2>PyWideStringList<a class="headerlink" href="#pywidestringlist" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyWideStringList">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyWideStringList</span></span></span><a class="headerlink" href="#c.PyWideStringList" title="Link to this definition">¶</a><br /></dt>
<dd><p>List of <code class="docutils literal notranslate"><span class="pre">wchar_t*</span></code> strings.</p>
<p>If <em>length</em> is non-zero, <em>items</em> must be non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code> and all strings must be
non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Methods:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyWideStringList_Append">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyWideStringList_Append</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">list</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">item</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyWideStringList_Append" title="Link to this definition">¶</a><br /></dt>
<dd><p>Append <em>item</em> to <em>list</em>.</p>
<p>Python must be preinitialized to call this function.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyWideStringList_Insert">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyWideStringList_Insert</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">list</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">index</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">item</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyWideStringList_Insert" title="Link to this definition">¶</a><br /></dt>
<dd><p>Insert <em>item</em> into <em>list</em> at <em>index</em>.</p>
<p>If <em>index</em> is greater than or equal to <em>list</em> length, append <em>item</em> to
<em>list</em>.</p>
<p><em>index</em> must be greater than or equal to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p>Python must be preinitialized to call this function.</p>
</dd></dl>

<p>Structure fields:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyWideStringList.length">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">length</span></span></span><a class="headerlink" href="#c.PyWideStringList.length" title="Link to this definition">¶</a><br /></dt>
<dd><p>List length.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyWideStringList.items">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">items</span></span></span><a class="headerlink" href="#c.PyWideStringList.items" title="Link to this definition">¶</a><br /></dt>
<dd><p>List items.</p>
</dd></dl>

</dd></dl>

</section>
<section id="pystatus">
<h2>PyStatus<a class="headerlink" href="#pystatus" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyStatus">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus</span></span></span><a class="headerlink" href="#c.PyStatus" title="Link to this definition">¶</a><br /></dt>
<dd><p>Structure to store an initialization function status: success, error
or exit.</p>
<p>For an error, it can store the C function name which created the error.</p>
<p>Structure fields:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyStatus.exitcode">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">exitcode</span></span></span><a class="headerlink" href="#c.PyStatus.exitcode" title="Link to this definition">¶</a><br /></dt>
<dd><p>Exit code. Argument passed to <code class="docutils literal notranslate"><span class="pre">exit()</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyStatus.err_msg">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">err_msg</span></span></span><a class="headerlink" href="#c.PyStatus.err_msg" title="Link to this definition">¶</a><br /></dt>
<dd><p>Error message.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyStatus.func">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">func</span></span></span><a class="headerlink" href="#c.PyStatus.func" title="Link to this definition">¶</a><br /></dt>
<dd><p>Name of the function which created an error, can be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<p>Functions to create a status:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyStatus_Ok">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus_Ok</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyStatus_Ok" title="Link to this definition">¶</a><br /></dt>
<dd><p>Success.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyStatus_Error">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus_Error</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">err_msg</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyStatus_Error" title="Link to this definition">¶</a><br /></dt>
<dd><p>Initialization error with a message.</p>
<p><em>err_msg</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyStatus_NoMemory">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus_NoMemory</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyStatus_NoMemory" title="Link to this definition">¶</a><br /></dt>
<dd><p>Memory allocation failure (out of memory).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyStatus_Exit">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus_Exit</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">exitcode</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyStatus_Exit" title="Link to this definition">¶</a><br /></dt>
<dd><p>Exit Python with the specified exit code.</p>
</dd></dl>

<p>Functions to handle a status:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyStatus_Exception">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus_Exception</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="n"><span class="pre">status</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyStatus_Exception" title="Link to this definition">¶</a><br /></dt>
<dd><p>Is the status an error or an exit? If true, the exception must be
handled; by calling <a class="reference internal" href="#c.Py_ExitStatusException" title="Py_ExitStatusException"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_ExitStatusException()</span></code></a> for example.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyStatus_IsError">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus_IsError</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="n"><span class="pre">status</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyStatus_IsError" title="Link to this definition">¶</a><br /></dt>
<dd><p>Is the result an error?</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyStatus_IsExit">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyStatus_IsExit</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="n"><span class="pre">status</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyStatus_IsExit" title="Link to this definition">¶</a><br /></dt>
<dd><p>Is the result an exit?</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_ExitStatusException">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_ExitStatusException</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="n"><span class="pre">status</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_ExitStatusException" title="Link to this definition">¶</a><br /></dt>
<dd><p>Call <code class="docutils literal notranslate"><span class="pre">exit(exitcode)</span></code> if <em>status</em> is an exit. Print the error
message and exit with a non-zero exit code if <em>status</em> is an error.  Must
only be called if <code class="docutils literal notranslate"><span class="pre">PyStatus_Exception(status)</span></code> is non-zero.</p>
</dd></dl>

</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Internally, Python uses macros which set <code class="docutils literal notranslate"><span class="pre">PyStatus.func</span></code>,
whereas functions to create a status set <code class="docutils literal notranslate"><span class="pre">func</span></code> to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</div>
<p>Example:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyStatus</span><span class="w"> </span><span class="nf">alloc</span><span class="p">(</span><span class="kt">void</span><span class="w"> </span><span class="o">**</span><span class="n">ptr</span><span class="p">,</span><span class="w"> </span><span class="kt">size_t</span><span class="w"> </span><span class="n">size</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="o">*</span><span class="n">ptr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyMem_RawMalloc</span><span class="p">(</span><span class="n">size</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">*</span><span class="n">ptr</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="nb">NULL</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PyStatus_NoMemory</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">PyStatus_Ok</span><span class="p">();</span>
<span class="p">}</span>

<span class="kt">int</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">argc</span><span class="p">,</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">**</span><span class="n">argv</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="n">ptr</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyStatus</span><span class="w"> </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">alloc</span><span class="p">(</span><span class="o">&amp;</span><span class="n">ptr</span><span class="p">,</span><span class="w"> </span><span class="mi">16</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Py_ExitStatusException</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="n">PyMem_Free</span><span class="p">(</span><span class="n">ptr</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="pypreconfig">
<h2>PyPreConfig<a class="headerlink" href="#pypreconfig" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyPreConfig">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyPreConfig</span></span></span><a class="headerlink" href="#c.PyPreConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Structure used to preinitialize Python.</p>
<p>Function to initialize a preconfiguration:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyPreConfig_InitPythonConfig">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyPreConfig_InitPythonConfig</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><span class="n"><span class="pre">PyPreConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">preconfig</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyPreConfig_InitPythonConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Initialize the preconfiguration with <a class="reference internal" href="#init-python-config"><span class="std std-ref">Python Configuration</span></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyPreConfig_InitIsolatedConfig">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyPreConfig_InitIsolatedConfig</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><span class="n"><span class="pre">PyPreConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">preconfig</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyPreConfig_InitIsolatedConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Initialize the preconfiguration with <a class="reference internal" href="#init-isolated-conf"><span class="std std-ref">Isolated Configuration</span></a>.</p>
</dd></dl>

<p>Structure fields:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.allocator">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">allocator</span></span></span><a class="headerlink" href="#c.PyPreConfig.allocator" title="Link to this definition">¶</a><br /></dt>
<dd><p>Name of the Python memory allocators:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_NOT_SET</span></code> (<code class="docutils literal notranslate"><span class="pre">0</span></code>): don’t change memory allocators
(use defaults).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_DEFAULT</span></code> (<code class="docutils literal notranslate"><span class="pre">1</span></code>): <a class="reference internal" href="memory.html#default-memory-allocators"><span class="std std-ref">default memory allocators</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_DEBUG</span></code> (<code class="docutils literal notranslate"><span class="pre">2</span></code>): <a class="reference internal" href="memory.html#default-memory-allocators"><span class="std std-ref">default memory allocators</span></a> with <a class="reference internal" href="memory.html#pymem-debug-hooks"><span class="std std-ref">debug hooks</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_MALLOC</span></code> (<code class="docutils literal notranslate"><span class="pre">3</span></code>): use <code class="docutils literal notranslate"><span class="pre">malloc()</span></code> of the C library.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_MALLOC_DEBUG</span></code> (<code class="docutils literal notranslate"><span class="pre">4</span></code>): force usage of
<code class="docutils literal notranslate"><span class="pre">malloc()</span></code> with <a class="reference internal" href="memory.html#pymem-debug-hooks"><span class="std std-ref">debug hooks</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_PYMALLOC</span></code> (<code class="docutils literal notranslate"><span class="pre">5</span></code>): <a class="reference internal" href="memory.html#pymalloc"><span class="std std-ref">Python pymalloc memory
allocator</span></a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_PYMALLOC_DEBUG</span></code> (<code class="docutils literal notranslate"><span class="pre">6</span></code>): <a class="reference internal" href="memory.html#pymalloc"><span class="std std-ref">Python pymalloc
memory allocator</span></a> with <a class="reference internal" href="memory.html#pymem-debug-hooks"><span class="std std-ref">debug hooks</span></a>.</p></li>
</ul>
<p><code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_PYMALLOC</span></code> and <code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_PYMALLOC_DEBUG</span></code> are
not supported if Python is <a class="reference internal" href="../using/configure.html#cmdoption-without-pymalloc"><code class="xref std std-option docutils literal notranslate"><span class="pre">configured</span> <span class="pre">using</span> <span class="pre">--without-pymalloc</span></code></a>.</p>
<p>See <a class="reference internal" href="memory.html#memory"><span class="std std-ref">Memory Management</span></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_NOT_SET</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.configure_locale">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">configure_locale</span></span></span><a class="headerlink" href="#c.PyPreConfig.configure_locale" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the LC_CTYPE locale to the user preferred locale.</p>
<p>If equals to <code class="docutils literal notranslate"><span class="pre">0</span></code>, set <a class="reference internal" href="#c.PyPreConfig.coerce_c_locale" title="PyPreConfig.coerce_c_locale"><code class="xref c c-member docutils literal notranslate"><span class="pre">coerce_c_locale</span></code></a> and
<a class="reference internal" href="#c.PyPreConfig.coerce_c_locale_warn" title="PyPreConfig.coerce_c_locale_warn"><code class="xref c c-member docutils literal notranslate"><span class="pre">coerce_c_locale_warn</span></code></a> members to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p>See the <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python config, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.coerce_c_locale">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">coerce_c_locale</span></span></span><a class="headerlink" href="#c.PyPreConfig.coerce_c_locale" title="Link to this definition">¶</a><br /></dt>
<dd><p>If equals to <code class="docutils literal notranslate"><span class="pre">2</span></code>, coerce the C locale.</p>
<p>If equals to <code class="docutils literal notranslate"><span class="pre">1</span></code>, read the LC_CTYPE locale to decide if it should be
coerced.</p>
<p>See the <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python config, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.coerce_c_locale_warn">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">coerce_c_locale_warn</span></span></span><a class="headerlink" href="#c.PyPreConfig.coerce_c_locale_warn" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, emit a warning if the C locale is coerced.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python config, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.dev_mode">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">dev_mode</span></span></span><a class="headerlink" href="#c.PyPreConfig.dev_mode" title="Link to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="../library/devmode.html#devmode"><span class="std std-ref">Python Development Mode</span></a>: see
<a class="reference internal" href="#c.PyConfig.dev_mode" title="PyConfig.dev_mode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.dev_mode</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.isolated">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">isolated</span></span></span><a class="headerlink" href="#c.PyPreConfig.isolated" title="Link to this definition">¶</a><br /></dt>
<dd><p>Isolated mode: see <a class="reference internal" href="#c.PyConfig.isolated" title="PyConfig.isolated"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.isolated</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">1</span></code> in isolated mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.legacy_windows_fs_encoding">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">legacy_windows_fs_encoding</span></span></span><a class="headerlink" href="#c.PyPreConfig.legacy_windows_fs_encoding" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero:</p>
<ul class="simple">
<li><p>Set <a class="reference internal" href="#c.PyPreConfig.utf8_mode" title="PyPreConfig.utf8_mode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.utf8_mode</span></code></a> to <code class="docutils literal notranslate"><span class="pre">0</span></code>,</p></li>
<li><p>Set <a class="reference internal" href="#c.PyConfig.filesystem_encoding" title="PyConfig.filesystem_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.filesystem_encoding</span></code></a> to <code class="docutils literal notranslate"><span class="pre">&quot;mbcs&quot;</span></code>,</p></li>
<li><p>Set <a class="reference internal" href="#c.PyConfig.filesystem_errors" title="PyConfig.filesystem_errors"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.filesystem_errors</span></code></a> to <code class="docutils literal notranslate"><span class="pre">&quot;replace&quot;</span></code>.</p></li>
</ul>
<p>Initialized the from <span class="target" id="index-1"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONLEGACYWINDOWSFSENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONLEGACYWINDOWSFSENCODING</span></code></a> environment
variable value.</p>
<p>Only available on Windows. <code class="docutils literal notranslate"><span class="pre">#ifdef</span> <span class="pre">MS_WINDOWS</span></code> macro can be used for
Windows specific code.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.parse_argv">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">parse_argv</span></span></span><a class="headerlink" href="#c.PyPreConfig.parse_argv" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, <a class="reference internal" href="#c.Py_PreInitializeFromArgs" title="Py_PreInitializeFromArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_PreInitializeFromArgs()</span></code></a> and
<a class="reference internal" href="#c.Py_PreInitializeFromBytesArgs" title="Py_PreInitializeFromBytesArgs"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_PreInitializeFromBytesArgs()</span></code></a> parse their <code class="docutils literal notranslate"><span class="pre">argv</span></code> argument the
same way the regular Python parses command line arguments: see
<a class="reference internal" href="../using/cmdline.html#using-on-cmdline"><span class="std std-ref">Command Line Arguments</span></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python config, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.use_environment">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">use_environment</span></span></span><a class="headerlink" href="#c.PyPreConfig.use_environment" title="Link to this definition">¶</a><br /></dt>
<dd><p>Use <a class="reference internal" href="../using/cmdline.html#using-on-envvars"><span class="std std-ref">environment variables</span></a>? See
<a class="reference internal" href="#c.PyConfig.use_environment" title="PyConfig.use_environment"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.use_environment</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python config and <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyPreConfig.utf8_mode">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">utf8_mode</span></span></span><a class="headerlink" href="#c.PyPreConfig.utf8_mode" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, enable the <a class="reference internal" href="../library/os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">utf8</span></code></a> command line option
and the <span class="target" id="index-2"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUTF8"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUTF8</span></code></a> environment variable.</p>
<p>Also set to <code class="docutils literal notranslate"><span class="pre">1</span></code> if the <code class="docutils literal notranslate"><span class="pre">LC_CTYPE</span></code> locale is <code class="docutils literal notranslate"><span class="pre">C</span></code> or <code class="docutils literal notranslate"><span class="pre">POSIX</span></code>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python config and <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

</dd></dl>

</section>
<section id="preinitialize-python-with-pypreconfig">
<span id="c-preinit"></span><h2>Preinitialize Python with PyPreConfig<a class="headerlink" href="#preinitialize-python-with-pypreconfig" title="Link to this heading">¶</a></h2>
<p>The preinitialization of Python:</p>
<ul class="simple">
<li><p>Set the Python memory allocators (<a class="reference internal" href="#c.PyPreConfig.allocator" title="PyPreConfig.allocator"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.allocator</span></code></a>)</p></li>
<li><p>Configure the LC_CTYPE locale (<a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a>)</p></li>
<li><p>Set the <a class="reference internal" href="../library/os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a>
(<a class="reference internal" href="#c.PyPreConfig.utf8_mode" title="PyPreConfig.utf8_mode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.utf8_mode</span></code></a>)</p></li>
</ul>
<p>The current preconfiguration (<code class="docutils literal notranslate"><span class="pre">PyPreConfig</span></code> type) is stored in
<code class="docutils literal notranslate"><span class="pre">_PyRuntime.preconfig</span></code>.</p>
<p>Functions to preinitialize Python:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_PreInitialize">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_PreInitialize</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><span class="n"><span class="pre">PyPreConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">preconfig</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_PreInitialize" title="Link to this definition">¶</a><br /></dt>
<dd><p>Preinitialize Python from <em>preconfig</em> preconfiguration.</p>
<p><em>preconfig</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_PreInitializeFromBytesArgs">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_PreInitializeFromBytesArgs</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><span class="n"><span class="pre">PyPreConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">preconfig</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_PreInitializeFromBytesArgs" title="Link to this definition">¶</a><br /></dt>
<dd><p>Preinitialize Python from <em>preconfig</em> preconfiguration.</p>
<p>Parse <em>argv</em> command line arguments (bytes strings) if
<a class="reference internal" href="#c.PyPreConfig.parse_argv" title="PyPreConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">parse_argv</span></code></a> of <em>preconfig</em> is non-zero.</p>
<p><em>preconfig</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_PreInitializeFromArgs">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_PreInitializeFromArgs</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><span class="n"><span class="pre">PyPreConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">preconfig</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_PreInitializeFromArgs" title="Link to this definition">¶</a><br /></dt>
<dd><p>Preinitialize Python from <em>preconfig</em> preconfiguration.</p>
<p>Parse <em>argv</em> command line arguments (wide strings) if
<a class="reference internal" href="#c.PyPreConfig.parse_argv" title="PyPreConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">parse_argv</span></code></a> of <em>preconfig</em> is non-zero.</p>
<p><em>preconfig</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<p>The caller is responsible to handle exceptions (error or exit) using
<a class="reference internal" href="#c.PyStatus_Exception" title="PyStatus_Exception"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyStatus_Exception()</span></code></a> and <a class="reference internal" href="#c.Py_ExitStatusException" title="Py_ExitStatusException"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_ExitStatusException()</span></code></a>.</p>
<p>For <a class="reference internal" href="#init-python-config"><span class="std std-ref">Python Configuration</span></a>
(<a class="reference internal" href="#c.PyPreConfig_InitPythonConfig" title="PyPreConfig_InitPythonConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyPreConfig_InitPythonConfig()</span></code></a>), if Python is initialized with
command line arguments, the command line arguments must also be passed to
preinitialize Python, since they have an effect on the pre-configuration
like encodings. For example, the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">utf8</span></code></a> command line option
enables the <a class="reference internal" href="../library/os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a>.</p>
<p><code class="docutils literal notranslate"><span class="pre">PyMem_SetAllocator()</span></code> can be called after <a class="reference internal" href="#c.Py_PreInitialize" title="Py_PreInitialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_PreInitialize()</span></code></a> and
before <a class="reference internal" href="#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> to install a custom memory allocator.
It can be called before <a class="reference internal" href="#c.Py_PreInitialize" title="Py_PreInitialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_PreInitialize()</span></code></a> if
<a class="reference internal" href="#c.PyPreConfig.allocator" title="PyPreConfig.allocator"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.allocator</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">PYMEM_ALLOCATOR_NOT_SET</span></code>.</p>
<p>Python memory allocation functions like <a class="reference internal" href="memory.html#c.PyMem_RawMalloc" title="PyMem_RawMalloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawMalloc()</span></code></a> must not be
used before the Python preinitialization, whereas calling directly <code class="docutils literal notranslate"><span class="pre">malloc()</span></code>
and <code class="docutils literal notranslate"><span class="pre">free()</span></code> is always safe. <a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a> must not be called
before the Python preinitialization.</p>
<p>Example using the preinitialization to enable
the <a class="reference internal" href="../library/os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a>:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyStatus</span><span class="w"> </span><span class="n">status</span><span class="p">;</span>
<span class="n">PyPreConfig</span><span class="w"> </span><span class="n">preconfig</span><span class="p">;</span>
<span class="n">PyPreConfig_InitPythonConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">preconfig</span><span class="p">);</span>

<span class="n">preconfig</span><span class="p">.</span><span class="n">utf8_mode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>

<span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_PreInitialize</span><span class="p">(</span><span class="o">&amp;</span><span class="n">preconfig</span><span class="p">);</span>
<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">Py_ExitStatusException</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="p">}</span>

<span class="cm">/* at this point, Python speaks UTF-8 */</span>

<span class="n">Py_Initialize</span><span class="p">();</span>
<span class="cm">/* ... use Python API here ... */</span>
<span class="n">Py_Finalize</span><span class="p">();</span>
</pre></div>
</div>
</section>
<section id="pyconfig">
<h2>PyConfig<a class="headerlink" href="#pyconfig" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyConfig">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig</span></span></span><a class="headerlink" href="#c.PyConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Structure containing most parameters to configure Python.</p>
<p>When done, the <a class="reference internal" href="#c.PyConfig_Clear" title="PyConfig_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_Clear()</span></code></a> function must be used to release the
configuration memory.</p>
<p>Structure methods:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_InitPythonConfig">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_InitPythonConfig</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_InitPythonConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Initialize configuration with the <a class="reference internal" href="#init-python-config"><span class="std std-ref">Python Configuration</span></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_InitIsolatedConfig">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_InitIsolatedConfig</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_InitIsolatedConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Initialize configuration with the <a class="reference internal" href="#init-isolated-conf"><span class="std std-ref">Isolated Configuration</span></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_SetString">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_SetString</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config_str</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_SetString" title="Link to this definition">¶</a><br /></dt>
<dd><p>Copy the wide character string <em>str</em> into <code class="docutils literal notranslate"><span class="pre">*config_str</span></code>.</p>
<p><a class="reference internal" href="#c-preinit"><span class="std std-ref">Preinitialize Python</span></a> if needed.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_SetBytesString">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_SetBytesString</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config_str</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_SetBytesString" title="Link to this definition">¶</a><br /></dt>
<dd><p>Decode <em>str</em> using <a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a> and set the result into
<code class="docutils literal notranslate"><span class="pre">*config_str</span></code>.</p>
<p><a class="reference internal" href="#c-preinit"><span class="std std-ref">Preinitialize Python</span></a> if needed.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_SetArgv">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_SetArgv</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_SetArgv" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set command line arguments (<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> member of
<em>config</em>) from the <em>argv</em> list of wide character strings.</p>
<p><a class="reference internal" href="#c-preinit"><span class="std std-ref">Preinitialize Python</span></a> if needed.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_SetBytesArgv">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_SetBytesArgv</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_SetBytesArgv" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set command line arguments (<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> member of
<em>config</em>) from the <em>argv</em> list of bytes strings. Decode bytes using
<a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a>.</p>
<p><a class="reference internal" href="#c-preinit"><span class="std std-ref">Preinitialize Python</span></a> if needed.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_SetWideStringList">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_SetWideStringList</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span>, <a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">list</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">length</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">items</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_SetWideStringList" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the list of wide strings <em>list</em> to <em>length</em> and <em>items</em>.</p>
<p><a class="reference internal" href="#c-preinit"><span class="std std-ref">Preinitialize Python</span></a> if needed.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_Read">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_Read</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_Read" title="Link to this definition">¶</a><br /></dt>
<dd><p>Read all Python configuration.</p>
<p>Fields which are already initialized are left unchanged.</p>
<p>Fields for <a class="reference internal" href="#init-path-config"><span class="std std-ref">path configuration</span></a> are no longer
calculated or modified when calling this function, as of Python 3.11.</p>
<p>The <a class="reference internal" href="#c.PyConfig_Read" title="PyConfig_Read"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_Read()</span></code></a> function only parses
<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a> arguments once: <a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a>
is set to <code class="docutils literal notranslate"><span class="pre">2</span></code> after arguments are parsed. Since Python arguments are
strippped from <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a>, parsing arguments twice would
parse the application options as Python options.</p>
<p><a class="reference internal" href="#c-preinit"><span class="std std-ref">Preinitialize Python</span></a> if needed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a> arguments are now only parsed once,
<a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">2</span></code> after arguments are
parsed, and arguments are only parsed if
<a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a> equals <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#c.PyConfig_Read" title="PyConfig_Read"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_Read()</span></code></a> no longer calculates all paths, and so fields
listed under <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> may
no longer be updated until <a class="reference internal" href="#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> is
called.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyConfig_Clear">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyConfig_Clear</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyConfig_Clear" title="Link to this definition">¶</a><br /></dt>
<dd><p>Release configuration memory.</p>
</dd></dl>

<p>Most <code class="docutils literal notranslate"><span class="pre">PyConfig</span></code> methods <a class="reference internal" href="#c-preinit"><span class="std std-ref">preinitialize Python</span></a> if needed.
In that case, the Python preinitialization configuration
(<a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyPreConfig</span></code></a>) in based on the <a class="reference internal" href="#c.PyConfig" title="PyConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyConfig</span></code></a>. If configuration
fields which are in common with <a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyPreConfig</span></code></a> are tuned, they must
be set before calling a <a class="reference internal" href="#c.PyConfig" title="PyConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyConfig</span></code></a> method:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#c.PyConfig.dev_mode" title="PyConfig.dev_mode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.dev_mode</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.isolated" title="PyConfig.isolated"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.isolated</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.use_environment" title="PyConfig.use_environment"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.use_environment</span></code></a></p></li>
</ul>
<p>Moreover, if <a class="reference internal" href="#c.PyConfig_SetArgv" title="PyConfig_SetArgv"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_SetArgv()</span></code></a> or <a class="reference internal" href="#c.PyConfig_SetBytesArgv" title="PyConfig_SetBytesArgv"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_SetBytesArgv()</span></code></a>
is used, this method must be called before other methods, since the
preinitialization configuration depends on command line arguments (if
<a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">parse_argv</span></code></a> is non-zero).</p>
<p>The caller of these methods is responsible to handle exceptions (error or
exit) using <code class="docutils literal notranslate"><span class="pre">PyStatus_Exception()</span></code> and <code class="docutils literal notranslate"><span class="pre">Py_ExitStatusException()</span></code>.</p>
<p>Structure fields:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.argv">
<a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">argv</span></span></span><a class="headerlink" href="#c.PyConfig.argv" title="Link to this definition">¶</a><br /></dt>
<dd><p>Command line arguments: <a class="reference internal" href="../library/sys.html#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a>.</p>
<p>Set <a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">parse_argv</span></code></a> to <code class="docutils literal notranslate"><span class="pre">1</span></code> to parse
<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> the same way the regular Python parses Python
command line arguments and then to strip Python arguments from
<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a>.</p>
<p>If <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> is empty, an empty string is added to
ensure that <a class="reference internal" href="../library/sys.html#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a> always exists and is never empty.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>See also the <a class="reference internal" href="#c.PyConfig.orig_argv" title="PyConfig.orig_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">orig_argv</span></code></a> member.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.safe_path">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">safe_path</span></span></span><a class="headerlink" href="#c.PyConfig.safe_path" title="Link to this definition">¶</a><br /></dt>
<dd><p>If equals to zero, <code class="docutils literal notranslate"><span class="pre">Py_RunMain()</span></code> prepends a potentially unsafe path to
<a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> at startup:</p>
<ul class="simple">
<li><p>If <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv[0]</span></code></a> is equal to <code class="docutils literal notranslate"><span class="pre">L&quot;-m&quot;</span></code>
(<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">module</span></code>), prepend the current working directory.</p></li>
<li><p>If running a script (<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">script.py</span></code>), prepend the script’s
directory.  If it’s a symbolic link, resolve symbolic links.</p></li>
<li><p>Otherwise (<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-c</span> <span class="pre">code</span></code> and <code class="docutils literal notranslate"><span class="pre">python</span></code>), prepend an empty string,
which means the current working directory.</p></li>
</ul>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-P"><code class="xref std std-option docutils literal notranslate"><span class="pre">-P</span></code></a> command line option and the
<span class="target" id="index-3"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONSAFEPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONSAFEPATH</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code> in Python config, <code class="docutils literal notranslate"><span class="pre">1</span></code> in isolated config.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.base_exec_prefix">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">base_exec_prefix</span></span></span><a class="headerlink" href="#c.PyConfig.base_exec_prefix" title="Link to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="../library/sys.html#sys.base_exec_prefix" title="sys.base_exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.base_exec_prefix</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> output.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.base_executable">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">base_executable</span></span></span><a class="headerlink" href="#c.PyConfig.base_executable" title="Link to this definition">¶</a><br /></dt>
<dd><p>Python base executable: <code class="xref py py-data docutils literal notranslate"><span class="pre">sys._base_executable</span></code>.</p>
<p>Set by the <span class="target" id="index-4"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">__PYVENV_LAUNCHER__</span></code> environment variable.</p>
<p>Set from <a class="reference internal" href="#c.PyConfig.executable" title="PyConfig.executable"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.executable</span></code></a> if <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> output.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.base_prefix">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">base_prefix</span></span></span><a class="headerlink" href="#c.PyConfig.base_prefix" title="Link to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="../library/sys.html#sys.base_prefix" title="sys.base_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.base_prefix</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> output.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.buffered_stdio">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">buffered_stdio</span></span></span><a class="headerlink" href="#c.PyConfig.buffered_stdio" title="Link to this definition">¶</a><br /></dt>
<dd><p>If equals to <code class="docutils literal notranslate"><span class="pre">0</span></code> and <a class="reference internal" href="#c.PyConfig.configure_c_stdio" title="PyConfig.configure_c_stdio"><code class="xref c c-member docutils literal notranslate"><span class="pre">configure_c_stdio</span></code></a> is non-zero,
disable buffering on the C streams stdout and stderr.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-u"><code class="xref std std-option docutils literal notranslate"><span class="pre">-u</span></code></a> command line option and the
<span class="target" id="index-5"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUNBUFFERED"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUNBUFFERED</span></code></a> environment variable.</p>
<p>stdin is always opened in buffered mode.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.bytes_warning">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">bytes_warning</span></span></span><a class="headerlink" href="#c.PyConfig.bytes_warning" title="Link to this definition">¶</a><br /></dt>
<dd><p>If equals to <code class="docutils literal notranslate"><span class="pre">1</span></code>, issue a warning when comparing <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or
<a class="reference internal" href="../library/stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> with <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, or comparing <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> with
<a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.</p>
<p>If equal or greater to <code class="docutils literal notranslate"><span class="pre">2</span></code>, raise a <a class="reference internal" href="../library/exceptions.html#BytesWarning" title="BytesWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BytesWarning</span></code></a> exception in these
cases.</p>
<p>Incremented by the <a class="reference internal" href="../using/cmdline.html#cmdoption-b"><code class="xref std std-option docutils literal notranslate"><span class="pre">-b</span></code></a> command line option.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.warn_default_encoding">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">warn_default_encoding</span></span></span><a class="headerlink" href="#c.PyConfig.warn_default_encoding" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, emit a <a class="reference internal" href="../library/exceptions.html#EncodingWarning" title="EncodingWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EncodingWarning</span></code></a> warning when <a class="reference internal" href="../library/io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a>
uses its default encoding. See <a class="reference internal" href="../library/io.html#io-encoding-warning"><span class="std std-ref">Opt-in EncodingWarning</span></a> for details.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.code_debug_ranges">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">code_debug_ranges</span></span></span><a class="headerlink" href="#c.PyConfig.code_debug_ranges" title="Link to this definition">¶</a><br /></dt>
<dd><p>If equals to <code class="docutils literal notranslate"><span class="pre">0</span></code>, disables the inclusion of the end line and column
mappings in code objects. Also disables traceback printing carets to
specific error locations.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> by the <span class="target" id="index-6"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONNODEBUGRANGES"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONNODEBUGRANGES</span></code></a> environment variable
and by the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">no_debug_ranges</span></code></a> command line option.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.check_hash_pycs_mode">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">check_hash_pycs_mode</span></span></span><a class="headerlink" href="#c.PyConfig.check_hash_pycs_mode" title="Link to this definition">¶</a><br /></dt>
<dd><p>Control the validation behavior of hash-based <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files:
value of the <a class="reference internal" href="../using/cmdline.html#cmdoption-check-hash-based-pycs"><code class="xref std std-option docutils literal notranslate"><span class="pre">--check-hash-based-pycs</span></code></a> command line option.</p>
<p>Valid values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">L&quot;always&quot;</span></code>: Hash the source file for invalidation regardless of
value of the ‘check_source’ flag.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">L&quot;never&quot;</span></code>: Assume that hash-based pycs always are valid.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">L&quot;default&quot;</span></code>: The ‘check_source’ flag in hash-based pycs
determines invalidation.</p></li>
</ul>
<p>Default: <code class="docutils literal notranslate"><span class="pre">L&quot;default&quot;</span></code>.</p>
<p>See also <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0552/"><strong>PEP 552</strong></a> “Deterministic pycs”.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.configure_c_stdio">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">configure_c_stdio</span></span></span><a class="headerlink" href="#c.PyConfig.configure_c_stdio" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, configure C standard streams:</p>
<ul class="simple">
<li><p>On Windows, set the binary mode (<code class="docutils literal notranslate"><span class="pre">O_BINARY</span></code>) on stdin, stdout and
stderr.</p></li>
<li><p>If <a class="reference internal" href="#c.PyConfig.buffered_stdio" title="PyConfig.buffered_stdio"><code class="xref c c-member docutils literal notranslate"><span class="pre">buffered_stdio</span></code></a> equals zero, disable buffering
of stdin, stdout and stderr streams.</p></li>
<li><p>If <a class="reference internal" href="#c.PyConfig.interactive" title="PyConfig.interactive"><code class="xref c c-member docutils literal notranslate"><span class="pre">interactive</span></code></a> is non-zero, enable stream
buffering on stdin and stdout (only stdout on Windows).</p></li>
</ul>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python config, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.dev_mode">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">dev_mode</span></span></span><a class="headerlink" href="#c.PyConfig.dev_mode" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, enable the <a class="reference internal" href="../library/devmode.html#devmode"><span class="std std-ref">Python Development Mode</span></a>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">dev</span></code></a> option and the
<span class="target" id="index-8"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDEVMODE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDEVMODE</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.dump_refs">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">dump_refs</span></span></span><a class="headerlink" href="#c.PyConfig.dump_refs" title="Link to this definition">¶</a><br /></dt>
<dd><p>Dump Python references?</p>
<p>If non-zero, dump all objects which are still alive at exit.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <span class="target" id="index-9"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDUMPREFS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDUMPREFS</span></code></a> environment variable.</p>
<p>Need a special build of Python with the <code class="docutils literal notranslate"><span class="pre">Py_TRACE_REFS</span></code> macro defined:
see the <a class="reference internal" href="../using/configure.html#cmdoption-with-trace-refs"><code class="xref std std-option docutils literal notranslate"><span class="pre">configure</span> <span class="pre">--with-trace-refs</span> <span class="pre">option</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.exec_prefix">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">exec_prefix</span></span></span><a class="headerlink" href="#c.PyConfig.exec_prefix" title="Link to this definition">¶</a><br /></dt>
<dd><p>The site-specific directory prefix where the platform-dependent Python
files are installed: <a class="reference internal" href="../library/sys.html#sys.exec_prefix" title="sys.exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> output.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.executable">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">executable</span></span></span><a class="headerlink" href="#c.PyConfig.executable" title="Link to this definition">¶</a><br /></dt>
<dd><p>The absolute path of the executable binary for the Python interpreter:
<a class="reference internal" href="../library/sys.html#sys.executable" title="sys.executable"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.executable</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> output.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.faulthandler">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">faulthandler</span></span></span><a class="headerlink" href="#c.PyConfig.faulthandler" title="Link to this definition">¶</a><br /></dt>
<dd><p>Enable faulthandler?</p>
<p>If non-zero, call <a class="reference internal" href="../library/faulthandler.html#faulthandler.enable" title="faulthandler.enable"><code class="xref py py-func docutils literal notranslate"><span class="pre">faulthandler.enable()</span></code></a> at startup.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">faulthandler</span></code></a> and the
<span class="target" id="index-10"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONFAULTHANDLER"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONFAULTHANDLER</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.filesystem_encoding">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">filesystem_encoding</span></span></span><a class="headerlink" href="#c.PyConfig.filesystem_encoding" title="Link to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">Filesystem encoding</span></a>:
<a class="reference internal" href="../library/sys.html#sys.getfilesystemencoding" title="sys.getfilesystemencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getfilesystemencoding()</span></code></a>.</p>
<p>On macOS, Android and VxWorks: use <code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code> by default.</p>
<p>On Windows: use <code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code> by default, or <code class="docutils literal notranslate"><span class="pre">&quot;mbcs&quot;</span></code> if
<a class="reference internal" href="#c.PyPreConfig.legacy_windows_fs_encoding" title="PyPreConfig.legacy_windows_fs_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">legacy_windows_fs_encoding</span></code></a> of
<a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyPreConfig</span></code></a> is non-zero.</p>
<p>Default encoding on other platforms:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code> if <a class="reference internal" href="#c.PyPreConfig.utf8_mode" title="PyPreConfig.utf8_mode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.utf8_mode</span></code></a> is non-zero.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;ascii&quot;</span></code> if Python detects that <code class="docutils literal notranslate"><span class="pre">nl_langinfo(CODESET)</span></code> announces
the ASCII encoding, whereas the <code class="docutils literal notranslate"><span class="pre">mbstowcs()</span></code> function
decodes from a different encoding (usually Latin1).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code> if <code class="docutils literal notranslate"><span class="pre">nl_langinfo(CODESET)</span></code> returns an empty string.</p></li>
<li><p>Otherwise, use the <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a>:
<code class="docutils literal notranslate"><span class="pre">nl_langinfo(CODESET)</span></code> result.</p></li>
</ul>
<p>At Python startup, the encoding name is normalized to the Python codec
name. For example, <code class="docutils literal notranslate"><span class="pre">&quot;ANSI_X3.4-1968&quot;</span></code> is replaced with <code class="docutils literal notranslate"><span class="pre">&quot;ascii&quot;</span></code>.</p>
<p>See also the <a class="reference internal" href="#c.PyConfig.filesystem_errors" title="PyConfig.filesystem_errors"><code class="xref c c-member docutils literal notranslate"><span class="pre">filesystem_errors</span></code></a> member.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.filesystem_errors">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">filesystem_errors</span></span></span><a class="headerlink" href="#c.PyConfig.filesystem_errors" title="Link to this definition">¶</a><br /></dt>
<dd><p><a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">Filesystem error handler</span></a>:
<a class="reference internal" href="../library/sys.html#sys.getfilesystemencodeerrors" title="sys.getfilesystemencodeerrors"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getfilesystemencodeerrors()</span></code></a>.</p>
<p>On Windows: use <code class="docutils literal notranslate"><span class="pre">&quot;surrogatepass&quot;</span></code> by default, or <code class="docutils literal notranslate"><span class="pre">&quot;replace&quot;</span></code>  if
<a class="reference internal" href="#c.PyPreConfig.legacy_windows_fs_encoding" title="PyPreConfig.legacy_windows_fs_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">legacy_windows_fs_encoding</span></code></a> of
<a class="reference internal" href="#c.PyPreConfig" title="PyPreConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyPreConfig</span></code></a> is non-zero.</p>
<p>On other platforms: use <code class="docutils literal notranslate"><span class="pre">&quot;surrogateescape&quot;</span></code> by default.</p>
<p>Supported error handlers:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;strict&quot;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;surrogateescape&quot;</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;surrogatepass&quot;</span></code> (only supported with the UTF-8 encoding)</p></li>
</ul>
<p>See also the <a class="reference internal" href="#c.PyConfig.filesystem_encoding" title="PyConfig.filesystem_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">filesystem_encoding</span></code></a> member.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.hash_seed">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">hash_seed</span></span></span><a class="headerlink" href="#c.PyConfig.hash_seed" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.use_hash_seed">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">use_hash_seed</span></span></span><a class="headerlink" href="#c.PyConfig.use_hash_seed" title="Link to this definition">¶</a><br /></dt>
<dd><p>Randomized hash function seed.</p>
<p>If <a class="reference internal" href="#c.PyConfig.use_hash_seed" title="PyConfig.use_hash_seed"><code class="xref c c-member docutils literal notranslate"><span class="pre">use_hash_seed</span></code></a> is zero, a seed is chosen randomly
at Python startup, and <a class="reference internal" href="#c.PyConfig.hash_seed" title="PyConfig.hash_seed"><code class="xref c c-member docutils literal notranslate"><span class="pre">hash_seed</span></code></a> is ignored.</p>
<p>Set by the <span class="target" id="index-11"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHASHSEED"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHASHSEED</span></code></a> environment variable.</p>
<p>Default <em>use_hash_seed</em> value: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated
mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.home">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">home</span></span></span><a class="headerlink" href="#c.PyConfig.home" title="Link to this definition">¶</a><br /></dt>
<dd><p>Python home directory.</p>
<p>If <a class="reference internal" href="init.html#c.Py_SetPythonHome" title="Py_SetPythonHome"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetPythonHome()</span></code></a> has been called, use its argument if it is
not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Set by the <span class="target" id="index-12"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHOME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHOME</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> input.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.import_time">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">import_time</span></span></span><a class="headerlink" href="#c.PyConfig.import_time" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, profile import time.</p>
<p>Set the <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">importtime</span></code></a> option and the
<span class="target" id="index-13"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPROFILEIMPORTTIME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPROFILEIMPORTTIME</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.inspect">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">inspect</span></span></span><a class="headerlink" href="#c.PyConfig.inspect" title="Link to this definition">¶</a><br /></dt>
<dd><p>Enter interactive mode after executing a script or a command.</p>
<p>If greater than <code class="docutils literal notranslate"><span class="pre">0</span></code>, enable inspect: when a script is passed as first
argument or the -c option is used, enter interactive mode after executing
the script or the command, even when <a class="reference internal" href="../library/sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a> does not appear to
be a terminal.</p>
<p>Incremented by the <a class="reference internal" href="../using/cmdline.html#cmdoption-i"><code class="xref std std-option docutils literal notranslate"><span class="pre">-i</span></code></a> command line option. Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> if the
<span class="target" id="index-14"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONINSPECT"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONINSPECT</span></code></a> environment variable is non-empty.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.install_signal_handlers">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">install_signal_handlers</span></span></span><a class="headerlink" href="#c.PyConfig.install_signal_handlers" title="Link to this definition">¶</a><br /></dt>
<dd><p>Install Python signal handlers?</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.interactive">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">interactive</span></span></span><a class="headerlink" href="#c.PyConfig.interactive" title="Link to this definition">¶</a><br /></dt>
<dd><p>If greater than <code class="docutils literal notranslate"><span class="pre">0</span></code>, enable the interactive mode (REPL).</p>
<p>Incremented by the <a class="reference internal" href="../using/cmdline.html#cmdoption-i"><code class="xref std std-option docutils literal notranslate"><span class="pre">-i</span></code></a> command line option.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.int_max_str_digits">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">int_max_str_digits</span></span></span><a class="headerlink" href="#c.PyConfig.int_max_str_digits" title="Link to this definition">¶</a><br /></dt>
<dd><p>Configures the <a class="reference internal" href="../library/stdtypes.html#int-max-str-digits"><span class="std std-ref">integer string conversion length limitation</span></a>.  An initial value of <code class="docutils literal notranslate"><span class="pre">-1</span></code> means the value will
be taken from the command line or environment or otherwise default to
4300 (<a class="reference internal" href="../library/sys.html#sys.int_info.default_max_str_digits" title="sys.int_info.default_max_str_digits"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.int_info.default_max_str_digits</span></code></a>).  A value of <code class="docutils literal notranslate"><span class="pre">0</span></code>
disables the limitation.  Values greater than zero but less than 640
(<a class="reference internal" href="../library/sys.html#sys.int_info.str_digits_check_threshold" title="sys.int_info.str_digits_check_threshold"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.int_info.str_digits_check_threshold</span></code></a>) are unsupported and
will produce an error.</p>
<p>Configured by the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">int_max_str_digits</span></code></a> command line
flag or the <span class="target" id="index-15"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONINTMAXSTRDIGITS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONINTMAXSTRDIGITS</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python mode.  4300
(<a class="reference internal" href="../library/sys.html#sys.int_info.default_max_str_digits" title="sys.int_info.default_max_str_digits"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.int_info.default_max_str_digits</span></code></a>) in isolated mode.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.isolated">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">isolated</span></span></span><a class="headerlink" href="#c.PyConfig.isolated" title="Link to this definition">¶</a><br /></dt>
<dd><p>If greater than <code class="docutils literal notranslate"><span class="pre">0</span></code>, enable isolated mode:</p>
<ul class="simple">
<li><p>Set <a class="reference internal" href="#c.PyConfig.safe_path" title="PyConfig.safe_path"><code class="xref c c-member docutils literal notranslate"><span class="pre">safe_path</span></code></a> to <code class="docutils literal notranslate"><span class="pre">1</span></code>:
don’t prepend a potentially unsafe path to <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> at Python
startup, such as the current directory, the script’s directory or an
empty string.</p></li>
<li><p>Set <a class="reference internal" href="#c.PyConfig.use_environment" title="PyConfig.use_environment"><code class="xref c c-member docutils literal notranslate"><span class="pre">use_environment</span></code></a> to <code class="docutils literal notranslate"><span class="pre">0</span></code>: ignore <code class="docutils literal notranslate"><span class="pre">PYTHON</span></code>
environment variables.</p></li>
<li><p>Set <a class="reference internal" href="#c.PyConfig.user_site_directory" title="PyConfig.user_site_directory"><code class="xref c c-member docutils literal notranslate"><span class="pre">user_site_directory</span></code></a> to <code class="docutils literal notranslate"><span class="pre">0</span></code>: don’t add the user
site directory to <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p></li>
<li><p>Python REPL doesn’t import <a class="reference internal" href="../library/readline.html#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> nor enable default readline
configuration on interactive prompts.</p></li>
</ul>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a> command line option.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">1</span></code> in isolated mode.</p>
<p>See also the <a class="reference internal" href="#init-isolated-conf"><span class="std std-ref">Isolated Configuration</span></a> and
<a class="reference internal" href="#c.PyPreConfig.isolated" title="PyPreConfig.isolated"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.isolated</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.legacy_windows_stdio">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">legacy_windows_stdio</span></span></span><a class="headerlink" href="#c.PyConfig.legacy_windows_stdio" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, use <a class="reference internal" href="../library/io.html#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.FileIO</span></code></a> instead of
<code class="xref py py-class docutils literal notranslate"><span class="pre">io._WindowsConsoleIO</span></code> for <a class="reference internal" href="../library/sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a>, <a class="reference internal" href="../library/sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a>
and <a class="reference internal" href="../library/sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> if the <span class="target" id="index-16"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONLEGACYWINDOWSSTDIO"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONLEGACYWINDOWSSTDIO</span></code></a> environment
variable is set to a non-empty string.</p>
<p>Only available on Windows. <code class="docutils literal notranslate"><span class="pre">#ifdef</span> <span class="pre">MS_WINDOWS</span></code> macro can be used for
Windows specific code.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p>See also the <span class="target" id="index-17"></span><a class="pep reference external" href="https://peps.python.org/pep-0528/"><strong>PEP 528</strong></a> (Change Windows console encoding to UTF-8).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.malloc_stats">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">malloc_stats</span></span></span><a class="headerlink" href="#c.PyConfig.malloc_stats" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, dump statistics on <a class="reference internal" href="memory.html#pymalloc"><span class="std std-ref">Python pymalloc memory allocator</span></a> at exit.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <span class="target" id="index-18"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONMALLOCSTATS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONMALLOCSTATS</span></code></a> environment variable.</p>
<p>The option is ignored if Python is <a class="reference internal" href="../using/configure.html#cmdoption-without-pymalloc"><code class="xref std std-option docutils literal notranslate"><span class="pre">configured</span> <span class="pre">using</span>
<span class="pre">the</span> <span class="pre">--without-pymalloc</span> <span class="pre">option</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.platlibdir">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">platlibdir</span></span></span><a class="headerlink" href="#c.PyConfig.platlibdir" title="Link to this definition">¶</a><br /></dt>
<dd><p>Platform library directory name: <a class="reference internal" href="../library/sys.html#sys.platlibdir" title="sys.platlibdir"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.platlibdir</span></code></a>.</p>
<p>Set by the <span class="target" id="index-19"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPLATLIBDIR"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPLATLIBDIR</span></code></a> environment variable.</p>
<p>Default: value of the <code class="docutils literal notranslate"><span class="pre">PLATLIBDIR</span></code> macro which is set by the
<a class="reference internal" href="../using/configure.html#cmdoption-with-platlibdir"><code class="xref std std-option docutils literal notranslate"><span class="pre">configure</span> <span class="pre">--with-platlibdir</span> <span class="pre">option</span></code></a>
(default: <code class="docutils literal notranslate"><span class="pre">&quot;lib&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;DLLs&quot;</span></code> on Windows).</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> input.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>This macro is now used on Windows to locate the standard
library extension modules, typically under <code class="docutils literal notranslate"><span class="pre">DLLs</span></code>. However,
for compatibility, note that this value is ignored for any
non-standard layouts, including in-tree builds and virtual
environments.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.pythonpath_env">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pythonpath_env</span></span></span><a class="headerlink" href="#c.PyConfig.pythonpath_env" title="Link to this definition">¶</a><br /></dt>
<dd><p>Module search paths (<a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>) as a string separated by <code class="docutils literal notranslate"><span class="pre">DELIM</span></code>
(<a class="reference internal" href="../library/os.html#os.pathsep" title="os.pathsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.pathsep</span></code></a>).</p>
<p>Set by the <span class="target" id="index-20"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPATH</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> input.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.module_search_paths">
<a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">module_search_paths</span></span></span><a class="headerlink" href="#c.PyConfig.module_search_paths" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.module_search_paths_set">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">module_search_paths_set</span></span></span><a class="headerlink" href="#c.PyConfig.module_search_paths_set" title="Link to this definition">¶</a><br /></dt>
<dd><p>Module search paths: <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
<p>If <a class="reference internal" href="#c.PyConfig.module_search_paths_set" title="PyConfig.module_search_paths_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">module_search_paths_set</span></code></a> is equal to <code class="docutils literal notranslate"><span class="pre">0</span></code>,
<a class="reference internal" href="#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> will replace
<a class="reference internal" href="#c.PyConfig.module_search_paths" title="PyConfig.module_search_paths"><code class="xref c c-member docutils literal notranslate"><span class="pre">module_search_paths</span></code></a> and sets
<a class="reference internal" href="#c.PyConfig.module_search_paths_set" title="PyConfig.module_search_paths_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">module_search_paths_set</span></code></a> to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<p>Default: empty list (<code class="docutils literal notranslate"><span class="pre">module_search_paths</span></code>) and <code class="docutils literal notranslate"><span class="pre">0</span></code>
(<code class="docutils literal notranslate"><span class="pre">module_search_paths_set</span></code>).</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> output.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.optimization_level">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">optimization_level</span></span></span><a class="headerlink" href="#c.PyConfig.optimization_level" title="Link to this definition">¶</a><br /></dt>
<dd><p>Compilation optimization level:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">0</span></code>: Peephole optimizer, set <code class="docutils literal notranslate"><span class="pre">__debug__</span></code> to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">1</span></code>: Level 0, remove assertions, set <code class="docutils literal notranslate"><span class="pre">__debug__</span></code> to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">2</span></code>: Level 1, strip docstrings.</p></li>
</ul>
<p>Incremented by the <a class="reference internal" href="../using/cmdline.html#cmdoption-O"><code class="xref std std-option docutils literal notranslate"><span class="pre">-O</span></code></a> command line option. Set to the
<span class="target" id="index-21"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONOPTIMIZE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONOPTIMIZE</span></code></a> environment variable value.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.orig_argv">
<a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">orig_argv</span></span></span><a class="headerlink" href="#c.PyConfig.orig_argv" title="Link to this definition">¶</a><br /></dt>
<dd><p>The list of the original command line arguments passed to the Python
executable: <a class="reference internal" href="../library/sys.html#sys.orig_argv" title="sys.orig_argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.orig_argv</span></code></a>.</p>
<p>If <a class="reference internal" href="#c.PyConfig.orig_argv" title="PyConfig.orig_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">orig_argv</span></code></a> list is empty and
<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> is not a list only containing an empty
string, <a class="reference internal" href="#c.PyConfig_Read" title="PyConfig_Read"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_Read()</span></code></a> copies <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> into
<a class="reference internal" href="#c.PyConfig.orig_argv" title="PyConfig.orig_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">orig_argv</span></code></a> before modifying
<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> (if <a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">parse_argv</span></code></a> is
non-zero).</p>
<p>See also the <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> member and the
<a class="reference internal" href="#c.Py_GetArgcArgv" title="Py_GetArgcArgv"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetArgcArgv()</span></code></a> function.</p>
<p>Default: empty list.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.parse_argv">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">parse_argv</span></span></span><a class="headerlink" href="#c.PyConfig.parse_argv" title="Link to this definition">¶</a><br /></dt>
<dd><p>Parse command line arguments?</p>
<p>If equals to <code class="docutils literal notranslate"><span class="pre">1</span></code>, parse <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> the same way the regular
Python parses <a class="reference internal" href="../using/cmdline.html#using-on-cmdline"><span class="std std-ref">command line arguments</span></a>, and strip
Python arguments from <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a>.</p>
<p>The <a class="reference internal" href="#c.PyConfig_Read" title="PyConfig_Read"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_Read()</span></code></a> function only parses
<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a> arguments once: <a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a>
is set to <code class="docutils literal notranslate"><span class="pre">2</span></code> after arguments are parsed. Since Python arguments are
strippped from <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a>, parsing arguments twice would
parse the application options as Python options.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a> arguments are now only parsed if
<a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a> equals to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.parser_debug">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">parser_debug</span></span></span><a class="headerlink" href="#c.PyConfig.parser_debug" title="Link to this definition">¶</a><br /></dt>
<dd><p>Parser debug mode. If greater than <code class="docutils literal notranslate"><span class="pre">0</span></code>, turn on parser debugging output (for expert only, depending
on compilation options).</p>
<p>Incremented by the <a class="reference internal" href="../using/cmdline.html#cmdoption-d"><code class="xref std std-option docutils literal notranslate"><span class="pre">-d</span></code></a> command line option. Set to the
<span class="target" id="index-22"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDEBUG"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDEBUG</span></code></a> environment variable value.</p>
<p>Need a <a class="reference internal" href="../using/configure.html#debug-build"><span class="std std-ref">debug build of Python</span></a> (the <code class="docutils literal notranslate"><span class="pre">Py_DEBUG</span></code> macro
must be defined).</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.pathconfig_warnings">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">pathconfig_warnings</span></span></span><a class="headerlink" href="#c.PyConfig.pathconfig_warnings" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, calculation of path configuration is allowed to log
warnings into <code class="docutils literal notranslate"><span class="pre">stderr</span></code>. If equals to <code class="docutils literal notranslate"><span class="pre">0</span></code>, suppress these warnings.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> input.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Now also applies on Windows.</p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.prefix">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">prefix</span></span></span><a class="headerlink" href="#c.PyConfig.prefix" title="Link to this definition">¶</a><br /></dt>
<dd><p>The site-specific directory prefix where the platform independent Python
files are installed: <a class="reference internal" href="../library/sys.html#sys.prefix" title="sys.prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.prefix</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> output.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.program_name">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">program_name</span></span></span><a class="headerlink" href="#c.PyConfig.program_name" title="Link to this definition">¶</a><br /></dt>
<dd><p>Program name used to initialize <a class="reference internal" href="#c.PyConfig.executable" title="PyConfig.executable"><code class="xref c c-member docutils literal notranslate"><span class="pre">executable</span></code></a> and in
early error messages during Python initialization.</p>
<ul class="simple">
<li><p>If <code class="xref py py-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code> has been called, use its argument.</p></li>
<li><p>On macOS, use <span class="target" id="index-23"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONEXECUTABLE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONEXECUTABLE</span></code></a> environment variable if set.</p></li>
<li><p>If the <code class="docutils literal notranslate"><span class="pre">WITH_NEXT_FRAMEWORK</span></code> macro is defined, use
<span class="target" id="index-24"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">__PYVENV_LAUNCHER__</span></code> environment variable if set.</p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">argv[0]</span></code> of <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a> if available and
non-empty.</p></li>
<li><p>Otherwise, use <code class="docutils literal notranslate"><span class="pre">L&quot;python&quot;</span></code> on Windows, or <code class="docutils literal notranslate"><span class="pre">L&quot;python3&quot;</span></code> on other
platforms.</p></li>
</ul>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Part of the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Python Path Configuration</span></a> input.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.pycache_prefix">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">pycache_prefix</span></span></span><a class="headerlink" href="#c.PyConfig.pycache_prefix" title="Link to this definition">¶</a><br /></dt>
<dd><p>Directory where cached <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files are written:
<a class="reference internal" href="../library/sys.html#sys.pycache_prefix" title="sys.pycache_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.pycache_prefix</span></code></a>.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">pycache_prefix=PATH</span></code></a> command line option and
the <span class="target" id="index-25"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPYCACHEPREFIX"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPYCACHEPREFIX</span></code></a> environment variable.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, <a class="reference internal" href="../library/sys.html#sys.pycache_prefix" title="sys.pycache_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.pycache_prefix</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.quiet">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">quiet</span></span></span><a class="headerlink" href="#c.PyConfig.quiet" title="Link to this definition">¶</a><br /></dt>
<dd><p>Quiet mode. If greater than <code class="docutils literal notranslate"><span class="pre">0</span></code>, don’t display the copyright and version at
Python startup in interactive mode.</p>
<p>Incremented by the <a class="reference internal" href="../using/cmdline.html#cmdoption-q"><code class="xref std std-option docutils literal notranslate"><span class="pre">-q</span></code></a> command line option.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.run_command">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">run_command</span></span></span><a class="headerlink" href="#c.PyConfig.run_command" title="Link to this definition">¶</a><br /></dt>
<dd><p>Value of the <a class="reference internal" href="../using/cmdline.html#cmdoption-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a> command line option.</p>
<p>Used by <a class="reference internal" href="#c.Py_RunMain" title="Py_RunMain"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_RunMain()</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.run_filename">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">run_filename</span></span></span><a class="headerlink" href="#c.PyConfig.run_filename" title="Link to this definition">¶</a><br /></dt>
<dd><p>Filename passed on the command line: trailing command line argument
without <a class="reference internal" href="../using/cmdline.html#cmdoption-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a> or <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a>. It is used by the
<a class="reference internal" href="#c.Py_RunMain" title="Py_RunMain"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_RunMain()</span></code></a> function.</p>
<p>For example, it is set to <code class="docutils literal notranslate"><span class="pre">script.py</span></code> by the <code class="docutils literal notranslate"><span class="pre">python3</span> <span class="pre">script.py</span> <span class="pre">arg</span></code>
command line.</p>
<p>See also the <a class="reference internal" href="#c.PyConfig.skip_source_first_line" title="PyConfig.skip_source_first_line"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.skip_source_first_line</span></code></a> option.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.run_module">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">run_module</span></span></span><a class="headerlink" href="#c.PyConfig.run_module" title="Link to this definition">¶</a><br /></dt>
<dd><p>Value of the <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a> command line option.</p>
<p>Used by <a class="reference internal" href="#c.Py_RunMain" title="Py_RunMain"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_RunMain()</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.show_ref_count">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">show_ref_count</span></span></span><a class="headerlink" href="#c.PyConfig.show_ref_count" title="Link to this definition">¶</a><br /></dt>
<dd><p>Show total reference count at exit (excluding immortal objects)?</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">showrefcount</span></code></a> command line option.</p>
<p>Need a <a class="reference internal" href="../using/configure.html#debug-build"><span class="std std-ref">debug build of Python</span></a> (the <code class="docutils literal notranslate"><span class="pre">Py_REF_DEBUG</span></code>
macro must be defined).</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.site_import">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">site_import</span></span></span><a class="headerlink" href="#c.PyConfig.site_import" title="Link to this definition">¶</a><br /></dt>
<dd><p>Import the <a class="reference internal" href="../library/site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module at startup?</p>
<p>If equal to zero, disable the import of the module site and the
site-dependent manipulations of <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> that it entails.</p>
<p>Also disable these manipulations if the <a class="reference internal" href="../library/site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module is explicitly
imported later (call <a class="reference internal" href="../library/site.html#site.main" title="site.main"><code class="xref py py-func docutils literal notranslate"><span class="pre">site.main()</span></code></a> if you want them to be triggered).</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a> command line option.</p>
<p><a class="reference internal" href="../library/sys.html#sys.flags" title="sys.flags"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.flags.no_site</span></code></a> is set to the inverted value of
<a class="reference internal" href="#c.PyConfig.site_import" title="PyConfig.site_import"><code class="xref c c-member docutils literal notranslate"><span class="pre">site_import</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.skip_source_first_line">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">skip_source_first_line</span></span></span><a class="headerlink" href="#c.PyConfig.skip_source_first_line" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, skip the first line of the <a class="reference internal" href="#c.PyConfig.run_filename" title="PyConfig.run_filename"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.run_filename</span></code></a>
source.</p>
<p>It allows the usage of non-Unix forms of <code class="docutils literal notranslate"><span class="pre">#!cmd</span></code>. This is intended for
a DOS specific hack only.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-x"><code class="xref std std-option docutils literal notranslate"><span class="pre">-x</span></code></a> command line option.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.stdio_encoding">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">stdio_encoding</span></span></span><a class="headerlink" href="#c.PyConfig.stdio_encoding" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.stdio_errors">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">stdio_errors</span></span></span><a class="headerlink" href="#c.PyConfig.stdio_errors" title="Link to this definition">¶</a><br /></dt>
<dd><p>Encoding and encoding errors of <a class="reference internal" href="../library/sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a>, <a class="reference internal" href="../library/sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a> and
<a class="reference internal" href="../library/sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a> (but <a class="reference internal" href="../library/sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a> always uses
<code class="docutils literal notranslate"><span class="pre">&quot;backslashreplace&quot;</span></code> error handler).</p>
<p>If <a class="reference internal" href="init.html#c.Py_SetStandardStreamEncoding" title="Py_SetStandardStreamEncoding"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetStandardStreamEncoding()</span></code></a> has been called, use its
<em>error</em> and <em>errors</em> arguments if they are not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>Use the <span class="target" id="index-26"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONIOENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONIOENCODING</span></code></a> environment variable if it is
non-empty.</p>
<p>Default encoding:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;UTF-8&quot;</span></code> if <a class="reference internal" href="#c.PyPreConfig.utf8_mode" title="PyPreConfig.utf8_mode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.utf8_mode</span></code></a> is non-zero.</p></li>
<li><p>Otherwise, use the <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a>.</p></li>
</ul>
<p>Default error handler:</p>
<ul class="simple">
<li><p>On Windows: use <code class="docutils literal notranslate"><span class="pre">&quot;surrogateescape&quot;</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;surrogateescape&quot;</span></code> if <a class="reference internal" href="#c.PyPreConfig.utf8_mode" title="PyPreConfig.utf8_mode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.utf8_mode</span></code></a> is non-zero,
or if the LC_CTYPE locale is “C” or “POSIX”.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;strict&quot;</span></code> otherwise.</p></li>
</ul>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.tracemalloc">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">tracemalloc</span></span></span><a class="headerlink" href="#c.PyConfig.tracemalloc" title="Link to this definition">¶</a><br /></dt>
<dd><p>Enable tracemalloc?</p>
<p>If non-zero, call <a class="reference internal" href="../library/tracemalloc.html#tracemalloc.start" title="tracemalloc.start"><code class="xref py py-func docutils literal notranslate"><span class="pre">tracemalloc.start()</span></code></a> at startup.</p>
<p>Set by <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">tracemalloc=N</span></code></a> command line option and by the
<span class="target" id="index-27"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONTRACEMALLOC"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONTRACEMALLOC</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.perf_profiling">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">perf_profiling</span></span></span><a class="headerlink" href="#c.PyConfig.perf_profiling" title="Link to this definition">¶</a><br /></dt>
<dd><p>Enable compatibility mode with the perf profiler?</p>
<p>If non-zero, initialize the perf trampoline. See <a class="reference internal" href="../howto/perf_profiling.html#perf-profiling"><span class="std std-ref">Python support for the Linux perf profiler</span></a>
for more information.</p>
<p>Set by <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">perf</span></code></a> command line option and by the
<span class="target" id="index-28"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPERFSUPPORT"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPERFSUPPORT</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.use_environment">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">use_environment</span></span></span><a class="headerlink" href="#c.PyConfig.use_environment" title="Link to this definition">¶</a><br /></dt>
<dd><p>Use <a class="reference internal" href="../using/cmdline.html#using-on-envvars"><span class="std std-ref">environment variables</span></a>?</p>
<p>If equals to zero, ignore the <a class="reference internal" href="../using/cmdline.html#using-on-envvars"><span class="std std-ref">environment variables</span></a>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-E"><code class="xref std std-option docutils literal notranslate"><span class="pre">-E</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python config and <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated config.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.user_site_directory">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">user_site_directory</span></span></span><a class="headerlink" href="#c.PyConfig.user_site_directory" title="Link to this definition">¶</a><br /></dt>
<dd><p>If non-zero, add the user site directory to <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-s"><code class="xref std std-option docutils literal notranslate"><span class="pre">-s</span></code></a> and <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a> command line options.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> by the <span class="target" id="index-29"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONNOUSERSITE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONNOUSERSITE</span></code></a> environment variable.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code> in Python mode, <code class="docutils literal notranslate"><span class="pre">0</span></code> in isolated mode.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.verbose">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">verbose</span></span></span><a class="headerlink" href="#c.PyConfig.verbose" title="Link to this definition">¶</a><br /></dt>
<dd><p>Verbose mode. If greater than <code class="docutils literal notranslate"><span class="pre">0</span></code>, print a message each time a module is
imported, showing the place (filename or built-in module) from which
it is loaded.</p>
<p>If greater than or equal to <code class="docutils literal notranslate"><span class="pre">2</span></code>, print a message for each file that is
checked for when searching for a module. Also provides information on
module cleanup at exit.</p>
<p>Incremented by the <a class="reference internal" href="../using/cmdline.html#cmdoption-v"><code class="xref std std-option docutils literal notranslate"><span class="pre">-v</span></code></a> command line option.</p>
<p>Set by the <span class="target" id="index-30"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONVERBOSE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONVERBOSE</span></code></a> environment variable value.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.warnoptions">
<a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">warnoptions</span></span></span><a class="headerlink" href="#c.PyConfig.warnoptions" title="Link to this definition">¶</a><br /></dt>
<dd><p>Options of the <a class="reference internal" href="../library/warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module to build warnings filters, lowest
to highest priority: <a class="reference internal" href="../library/sys.html#sys.warnoptions" title="sys.warnoptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.warnoptions</span></code></a>.</p>
<p>The <a class="reference internal" href="../library/warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module adds <a class="reference internal" href="../library/sys.html#sys.warnoptions" title="sys.warnoptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.warnoptions</span></code></a> in the reverse
order: the last <a class="reference internal" href="#c.PyConfig.warnoptions" title="PyConfig.warnoptions"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.warnoptions</span></code></a> item becomes the first
item of <code class="xref py py-data docutils literal notranslate"><span class="pre">warnings.filters</span></code> which is checked first (highest
priority).</p>
<p>The <a class="reference internal" href="../using/cmdline.html#cmdoption-W"><code class="xref std std-option docutils literal notranslate"><span class="pre">-W</span></code></a> command line options adds its value to
<a class="reference internal" href="#c.PyConfig.warnoptions" title="PyConfig.warnoptions"><code class="xref c c-member docutils literal notranslate"><span class="pre">warnoptions</span></code></a>, it can be used multiple times.</p>
<p>The <span class="target" id="index-31"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONWARNINGS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONWARNINGS</span></code></a> environment variable can also be used to add
warning options. Multiple options can be specified, separated by commas
(<code class="docutils literal notranslate"><span class="pre">,</span></code>).</p>
<p>Default: empty list.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.write_bytecode">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">write_bytecode</span></span></span><a class="headerlink" href="#c.PyConfig.write_bytecode" title="Link to this definition">¶</a><br /></dt>
<dd><p>If equal to <code class="docutils literal notranslate"><span class="pre">0</span></code>, Python won’t try to write <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files on the import of
source modules.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">0</span></code> by the <a class="reference internal" href="../using/cmdline.html#cmdoption-B"><code class="xref std std-option docutils literal notranslate"><span class="pre">-B</span></code></a> command line option and the
<span class="target" id="index-32"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDONTWRITEBYTECODE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDONTWRITEBYTECODE</span></code></a> environment variable.</p>
<p><a class="reference internal" href="../library/sys.html#sys.dont_write_bytecode" title="sys.dont_write_bytecode"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.dont_write_bytecode</span></code></a> is initialized to the inverted value of
<a class="reference internal" href="#c.PyConfig.write_bytecode" title="PyConfig.write_bytecode"><code class="xref c c-member docutils literal notranslate"><span class="pre">write_bytecode</span></code></a>.</p>
<p>Default: <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyConfig.xoptions">
<a class="reference internal" href="#c.PyWideStringList" title="PyWideStringList"><span class="n"><span class="pre">PyWideStringList</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">xoptions</span></span></span><a class="headerlink" href="#c.PyConfig.xoptions" title="Link to this definition">¶</a><br /></dt>
<dd><p>Values of the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> command line options: <a class="reference internal" href="../library/sys.html#sys._xoptions" title="sys._xoptions"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys._xoptions</span></code></a>.</p>
<p>Default: empty list.</p>
</dd></dl>

</dd></dl>

<p>If <a class="reference internal" href="#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">parse_argv</span></code></a> is non-zero, <a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a>
arguments are parsed the same way the regular Python parses <a class="reference internal" href="../using/cmdline.html#using-on-cmdline"><span class="std std-ref">command line
arguments</span></a>, and Python arguments are stripped from
<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">argv</span></code></a>.</p>
<p>The <a class="reference internal" href="#c.PyConfig.xoptions" title="PyConfig.xoptions"><code class="xref c c-member docutils literal notranslate"><span class="pre">xoptions</span></code></a> options are parsed to set other options: see
the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> command line option.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <code class="docutils literal notranslate"><span class="pre">show_alloc_count</span></code> field has been removed.</p>
</div>
</section>
<section id="initialization-with-pyconfig">
<h2>Initialization with PyConfig<a class="headerlink" href="#initialization-with-pyconfig" title="Link to this heading">¶</a></h2>
<p>Function to initialize Python:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_InitializeFromConfig">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_InitializeFromConfig</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.PyConfig" title="PyConfig"><span class="n"><span class="pre">PyConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_InitializeFromConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Initialize Python from <em>config</em> configuration.</p>
</dd></dl>

<p>The caller is responsible to handle exceptions (error or exit) using
<a class="reference internal" href="#c.PyStatus_Exception" title="PyStatus_Exception"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyStatus_Exception()</span></code></a> and <a class="reference internal" href="#c.Py_ExitStatusException" title="Py_ExitStatusException"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_ExitStatusException()</span></code></a>.</p>
<p>If <a class="reference internal" href="import.html#c.PyImport_FrozenModules" title="PyImport_FrozenModules"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_FrozenModules()</span></code></a>, <a class="reference internal" href="import.html#c.PyImport_AppendInittab" title="PyImport_AppendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_AppendInittab()</span></code></a> or
<a class="reference internal" href="import.html#c.PyImport_ExtendInittab" title="PyImport_ExtendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ExtendInittab()</span></code></a> are used, they must be set or called after
Python preinitialization and before the Python initialization. If Python is
initialized multiple times, <a class="reference internal" href="import.html#c.PyImport_AppendInittab" title="PyImport_AppendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_AppendInittab()</span></code></a> or
<a class="reference internal" href="import.html#c.PyImport_ExtendInittab" title="PyImport_ExtendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ExtendInittab()</span></code></a> must be called before each Python
initialization.</p>
<p>The current configuration (<code class="docutils literal notranslate"><span class="pre">PyConfig</span></code> type) is stored in
<code class="docutils literal notranslate"><span class="pre">PyInterpreterState.config</span></code>.</p>
<p>Example setting the program name:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">init_python</span><span class="p">(</span><span class="kt">void</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyStatus</span><span class="w"> </span><span class="n">status</span><span class="p">;</span>

<span class="w">    </span><span class="n">PyConfig</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyConfig_InitPythonConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>

<span class="w">    </span><span class="cm">/* Set the program name. Implicitly preinitialize Python. */</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyConfig_SetString</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">config</span><span class="p">.</span><span class="n">program_name</span><span class="p">,</span>
<span class="w">                                </span><span class="sa">L</span><span class="s">&quot;/path/to/my_program&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">exception</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_InitializeFromConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">exception</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="n">PyConfig_Clear</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="p">;</span>

<span class="nl">exception</span><span class="p">:</span>
<span class="w">    </span><span class="n">PyConfig_Clear</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="n">Py_ExitStatusException</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
<p>More complete example modifying the default configuration, read the
configuration, and then override some parameters. Note that since
3.11, many parameters are not calculated until initialization, and
so values cannot be read from the configuration structure. Any values
set before initialize is called will be left unchanged by
initialization:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyStatus</span><span class="w"> </span><span class="nf">init_python</span><span class="p">(</span><span class="k">const</span><span class="w"> </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">program_name</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyStatus</span><span class="w"> </span><span class="n">status</span><span class="p">;</span>

<span class="w">    </span><span class="n">PyConfig</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyConfig_InitPythonConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>

<span class="w">    </span><span class="cm">/* Set the program name before reading the configuration</span>
<span class="cm">       (decode byte string from the locale encoding).</span>

<span class="cm">       Implicitly preinitialize Python. */</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyConfig_SetBytesString</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">config</span><span class="p">.</span><span class="n">program_name</span><span class="p">,</span>
<span class="w">                                     </span><span class="n">program_name</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">done</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/* Read all configuration at once */</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyConfig_Read</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">done</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/* Specify sys.path explicitly */</span>
<span class="w">    </span><span class="cm">/* If you want to modify the default set of paths, finish</span>
<span class="cm">       initialization first and then use PySys_GetObject(&quot;path&quot;) */</span>
<span class="w">    </span><span class="n">config</span><span class="p">.</span><span class="n">module_search_paths_set</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyWideStringList_Append</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">.</span><span class="n">module_search_paths</span><span class="p">,</span>
<span class="w">                                     </span><span class="sa">L</span><span class="s">&quot;/path/to/stdlib&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">done</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyWideStringList_Append</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">.</span><span class="n">module_search_paths</span><span class="p">,</span>
<span class="w">                                     </span><span class="sa">L</span><span class="s">&quot;/path/to/more/modules&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">done</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/* Override executable computed by PyConfig_Read() */</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyConfig_SetString</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">,</span><span class="w"> </span><span class="o">&amp;</span><span class="n">config</span><span class="p">.</span><span class="n">executable</span><span class="p">,</span>
<span class="w">                                </span><span class="sa">L</span><span class="s">&quot;/path/to/my_executable&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">goto</span><span class="w"> </span><span class="n">done</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_InitializeFromConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>

<span class="nl">done</span><span class="p">:</span>
<span class="w">    </span><span class="n">PyConfig_Clear</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">status</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="isolated-configuration">
<span id="init-isolated-conf"></span><h2>Isolated Configuration<a class="headerlink" href="#isolated-configuration" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#c.PyPreConfig_InitIsolatedConfig" title="PyPreConfig_InitIsolatedConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyPreConfig_InitIsolatedConfig()</span></code></a> and
<a class="reference internal" href="#c.PyConfig_InitIsolatedConfig" title="PyConfig_InitIsolatedConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_InitIsolatedConfig()</span></code></a> functions create a configuration to
isolate Python from the system. For example, to embed Python into an
application.</p>
<p>This configuration ignores global configuration variables, environment
variables, command line arguments (<a class="reference internal" href="#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a> is not parsed)
and user site directory. The C standard streams (ex: <code class="docutils literal notranslate"><span class="pre">stdout</span></code>) and the
LC_CTYPE locale are left unchanged. Signal handlers are not installed.</p>
<p>Configuration files are still used with this configuration to determine
paths that are unspecified. Ensure <a class="reference internal" href="#c.PyConfig.home" title="PyConfig.home"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.home</span></code></a> is specified
to avoid computing the default path configuration.</p>
</section>
<section id="python-configuration">
<span id="init-python-config"></span><h2>Python Configuration<a class="headerlink" href="#python-configuration" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#c.PyPreConfig_InitPythonConfig" title="PyPreConfig_InitPythonConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyPreConfig_InitPythonConfig()</span></code></a> and <a class="reference internal" href="#c.PyConfig_InitPythonConfig" title="PyConfig_InitPythonConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_InitPythonConfig()</span></code></a>
functions create a configuration to build a customized Python which behaves as
the regular Python.</p>
<p>Environments variables and command line arguments are used to configure
Python, whereas global configuration variables are ignored.</p>
<p>This function enables C locale coercion (<span class="target" id="index-33"></span><a class="pep reference external" href="https://peps.python.org/pep-0538/"><strong>PEP 538</strong></a>)
and <a class="reference internal" href="../library/os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a>
(<span class="target" id="index-34"></span><a class="pep reference external" href="https://peps.python.org/pep-0540/"><strong>PEP 540</strong></a>) depending on the LC_CTYPE locale, <span class="target" id="index-35"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUTF8"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUTF8</span></code></a> and
<span class="target" id="index-36"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONCOERCECLOCALE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONCOERCECLOCALE</span></code></a> environment variables.</p>
</section>
<section id="python-path-configuration">
<span id="init-path-config"></span><h2>Python Path Configuration<a class="headerlink" href="#python-path-configuration" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#c.PyConfig" title="PyConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyConfig</span></code></a> contains multiple fields for the path configuration:</p>
<ul class="simple">
<li><p>Path configuration inputs:</p>
<ul>
<li><p><a class="reference internal" href="#c.PyConfig.home" title="PyConfig.home"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.home</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.platlibdir" title="PyConfig.platlibdir"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.platlibdir</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.pathconfig_warnings" title="PyConfig.pathconfig_warnings"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.pathconfig_warnings</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.program_name" title="PyConfig.program_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.program_name</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.pythonpath_env" title="PyConfig.pythonpath_env"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.pythonpath_env</span></code></a></p></li>
<li><p>current working directory: to get absolute paths</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PATH</span></code> environment variable to get the program full path
(from <a class="reference internal" href="#c.PyConfig.program_name" title="PyConfig.program_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.program_name</span></code></a>)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">__PYVENV_LAUNCHER__</span></code> environment variable</p></li>
<li><p>(Windows only) Application paths in the registry under
“SoftwarePythonPythonCoreX.YPythonPath” of HKEY_CURRENT_USER and
HKEY_LOCAL_MACHINE (where X.Y is the Python version).</p></li>
</ul>
</li>
<li><p>Path configuration output fields:</p>
<ul>
<li><p><a class="reference internal" href="#c.PyConfig.base_exec_prefix" title="PyConfig.base_exec_prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.base_exec_prefix</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.base_executable" title="PyConfig.base_executable"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.base_executable</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.base_prefix" title="PyConfig.base_prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.base_prefix</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.exec_prefix" title="PyConfig.exec_prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.exec_prefix</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.executable" title="PyConfig.executable"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.executable</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.module_search_paths_set" title="PyConfig.module_search_paths_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.module_search_paths_set</span></code></a>,
<a class="reference internal" href="#c.PyConfig.module_search_paths" title="PyConfig.module_search_paths"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.module_search_paths</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.PyConfig.prefix" title="PyConfig.prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.prefix</span></code></a></p></li>
</ul>
</li>
</ul>
<p>If at least one “output field” is not set, Python calculates the path
configuration to fill unset fields. If
<a class="reference internal" href="#c.PyConfig.module_search_paths_set" title="PyConfig.module_search_paths_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">module_search_paths_set</span></code></a> is equal to <code class="docutils literal notranslate"><span class="pre">0</span></code>,
<a class="reference internal" href="#c.PyConfig.module_search_paths" title="PyConfig.module_search_paths"><code class="xref c c-member docutils literal notranslate"><span class="pre">module_search_paths</span></code></a> is overridden and
<a class="reference internal" href="#c.PyConfig.module_search_paths_set" title="PyConfig.module_search_paths_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">module_search_paths_set</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<p>It is possible to completely ignore the function calculating the default
path configuration by setting explicitly all path configuration output
fields listed above. A string is considered as set even if it is non-empty.
<code class="docutils literal notranslate"><span class="pre">module_search_paths</span></code> is considered as set if
<code class="docutils literal notranslate"><span class="pre">module_search_paths_set</span></code> is set to <code class="docutils literal notranslate"><span class="pre">1</span></code>. In this case,
<code class="docutils literal notranslate"><span class="pre">module_search_paths</span></code> will be used without modification.</p>
<p>Set <a class="reference internal" href="#c.PyConfig.pathconfig_warnings" title="PyConfig.pathconfig_warnings"><code class="xref c c-member docutils literal notranslate"><span class="pre">pathconfig_warnings</span></code></a> to <code class="docutils literal notranslate"><span class="pre">0</span></code> to suppress warnings when
calculating the path configuration (Unix only, Windows does not log any warning).</p>
<p>If <a class="reference internal" href="#c.PyConfig.base_prefix" title="PyConfig.base_prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">base_prefix</span></code></a> or <a class="reference internal" href="#c.PyConfig.base_exec_prefix" title="PyConfig.base_exec_prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">base_exec_prefix</span></code></a>
fields are not set, they inherit their value from <a class="reference internal" href="#c.PyConfig.prefix" title="PyConfig.prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">prefix</span></code></a>
and <a class="reference internal" href="#c.PyConfig.exec_prefix" title="PyConfig.exec_prefix"><code class="xref c c-member docutils literal notranslate"><span class="pre">exec_prefix</span></code></a> respectively.</p>
<p><a class="reference internal" href="#c.Py_RunMain" title="Py_RunMain"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_RunMain()</span></code></a> and <a class="reference internal" href="veryhigh.html#c.Py_Main" title="Py_Main"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Main()</span></code></a> modify <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>:</p>
<ul class="simple">
<li><p>If <a class="reference internal" href="#c.PyConfig.run_filename" title="PyConfig.run_filename"><code class="xref c c-member docutils literal notranslate"><span class="pre">run_filename</span></code></a> is set and is a directory which contains a
<code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> script, prepend <a class="reference internal" href="#c.PyConfig.run_filename" title="PyConfig.run_filename"><code class="xref c c-member docutils literal notranslate"><span class="pre">run_filename</span></code></a> to
<a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p></li>
<li><p>If <a class="reference internal" href="#c.PyConfig.isolated" title="PyConfig.isolated"><code class="xref c c-member docutils literal notranslate"><span class="pre">isolated</span></code></a> is zero:</p>
<ul>
<li><p>If <a class="reference internal" href="#c.PyConfig.run_module" title="PyConfig.run_module"><code class="xref c c-member docutils literal notranslate"><span class="pre">run_module</span></code></a> is set, prepend the current directory
to <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>. Do nothing if the current directory cannot be read.</p></li>
<li><p>If <a class="reference internal" href="#c.PyConfig.run_filename" title="PyConfig.run_filename"><code class="xref c c-member docutils literal notranslate"><span class="pre">run_filename</span></code></a> is set, prepend the directory of the
filename to <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p></li>
<li><p>Otherwise, prepend an empty string to <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p></li>
</ul>
</li>
</ul>
<p>If <a class="reference internal" href="#c.PyConfig.site_import" title="PyConfig.site_import"><code class="xref c c-member docutils literal notranslate"><span class="pre">site_import</span></code></a> is non-zero, <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> can be
modified by the <a class="reference internal" href="../library/site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module. If
<a class="reference internal" href="#c.PyConfig.user_site_directory" title="PyConfig.user_site_directory"><code class="xref c c-member docutils literal notranslate"><span class="pre">user_site_directory</span></code></a> is non-zero and the user’s
site-package directory exists, the <a class="reference internal" href="../library/site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module appends the user’s
site-package directory to <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
<p>The following configuration files are used by the path configuration:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pyvenv.cfg</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">._pth</span></code> file (ex: <code class="docutils literal notranslate"><span class="pre">python._pth</span></code>)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pybuilddir.txt</span></code> (Unix only)</p></li>
</ul>
<p>If a <code class="docutils literal notranslate"><span class="pre">._pth</span></code> file is present:</p>
<ul class="simple">
<li><p>Set <a class="reference internal" href="#c.PyConfig.isolated" title="PyConfig.isolated"><code class="xref c c-member docutils literal notranslate"><span class="pre">isolated</span></code></a> to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p></li>
<li><p>Set <a class="reference internal" href="#c.PyConfig.use_environment" title="PyConfig.use_environment"><code class="xref c c-member docutils literal notranslate"><span class="pre">use_environment</span></code></a> to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p></li>
<li><p>Set <a class="reference internal" href="#c.PyConfig.site_import" title="PyConfig.site_import"><code class="xref c c-member docutils literal notranslate"><span class="pre">site_import</span></code></a> to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p></li>
<li><p>Set <a class="reference internal" href="#c.PyConfig.safe_path" title="PyConfig.safe_path"><code class="xref c c-member docutils literal notranslate"><span class="pre">safe_path</span></code></a> to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p></li>
</ul>
<p>The <code class="docutils literal notranslate"><span class="pre">__PYVENV_LAUNCHER__</span></code> environment variable is used to set
<a class="reference internal" href="#c.PyConfig.base_executable" title="PyConfig.base_executable"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.base_executable</span></code></a></p>
</section>
<section id="py-runmain">
<h2>Py_RunMain()<a class="headerlink" href="#py-runmain" title="Link to this heading">¶</a></h2>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_RunMain">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_RunMain</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_RunMain" title="Link to this definition">¶</a><br /></dt>
<dd><p>Execute the command (<a class="reference internal" href="#c.PyConfig.run_command" title="PyConfig.run_command"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.run_command</span></code></a>), the script
(<a class="reference internal" href="#c.PyConfig.run_filename" title="PyConfig.run_filename"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.run_filename</span></code></a>) or the module
(<a class="reference internal" href="#c.PyConfig.run_module" title="PyConfig.run_module"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.run_module</span></code></a>) specified on the command line or in the
configuration.</p>
<p>By default and when if <a class="reference internal" href="../using/cmdline.html#cmdoption-i"><code class="xref std std-option docutils literal notranslate"><span class="pre">-i</span></code></a> option is used, run the REPL.</p>
<p>Finally, finalizes Python and returns an exit status that can be passed to
the <code class="docutils literal notranslate"><span class="pre">exit()</span></code> function.</p>
</dd></dl>

<p>See <a class="reference internal" href="#init-python-config"><span class="std std-ref">Python Configuration</span></a> for an example of
customized Python always running in isolated mode using
<a class="reference internal" href="#c.Py_RunMain" title="Py_RunMain"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_RunMain()</span></code></a>.</p>
</section>
<section id="py-getargcargv">
<h2>Py_GetArgcArgv()<a class="headerlink" href="#py-getargcargv" title="Link to this heading">¶</a></h2>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetArgcArgv">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetArgcArgv</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argc</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetArgcArgv" title="Link to this definition">¶</a><br /></dt>
<dd><p>Get the original command line arguments, before Python modified them.</p>
<p>See also <a class="reference internal" href="#c.PyConfig.orig_argv" title="PyConfig.orig_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.orig_argv</span></code></a> member.</p>
</dd></dl>

</section>
<section id="multi-phase-initialization-private-provisional-api">
<h2>Multi-Phase Initialization Private Provisional API<a class="headerlink" href="#multi-phase-initialization-private-provisional-api" title="Link to this heading">¶</a></h2>
<p>This section is a private provisional API introducing multi-phase
initialization, the core feature of <span class="target" id="index-37"></span><a class="pep reference external" href="https://peps.python.org/pep-0432/"><strong>PEP 432</strong></a>:</p>
<ul class="simple">
<li><p>“Core” initialization phase, “bare minimum Python”:</p>
<ul>
<li><p>Builtin types;</p></li>
<li><p>Builtin exceptions;</p></li>
<li><p>Builtin and frozen modules;</p></li>
<li><p>The <a class="reference internal" href="../library/sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> module is only partially initialized
(ex: <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> doesn’t exist yet).</p></li>
</ul>
</li>
<li><p>“Main” initialization phase, Python is fully initialized:</p>
<ul>
<li><p>Install and configure <a class="reference internal" href="../library/importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a>;</p></li>
<li><p>Apply the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Path Configuration</span></a>;</p></li>
<li><p>Install signal handlers;</p></li>
<li><p>Finish <a class="reference internal" href="../library/sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> module initialization (ex: create <a class="reference internal" href="../library/sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a>
and <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>);</p></li>
<li><p>Enable optional features like <a class="reference internal" href="../library/faulthandler.html#module-faulthandler" title="faulthandler: Dump the Python traceback."><code class="xref py py-mod docutils literal notranslate"><span class="pre">faulthandler</span></code></a> and <a class="reference internal" href="../library/tracemalloc.html#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a>;</p></li>
<li><p>Import the <a class="reference internal" href="../library/site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module;</p></li>
<li><p>etc.</p></li>
</ul>
</li>
</ul>
<p>Private provisional API:</p>
<ul class="simple">
<li><p><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig._init_main</span></code>: if set to <code class="docutils literal notranslate"><span class="pre">0</span></code>,
<a class="reference internal" href="#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> stops at the “Core” initialization phase.</p></li>
</ul>
<dl class="c function">
<dt class="sig sig-object c" id="c._Py_InitializeMain">
<a class="reference internal" href="#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_Py_InitializeMain</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._Py_InitializeMain" title="Link to this definition">¶</a><br /></dt>
<dd><p>Move to the “Main” initialization phase, finish the Python initialization.</p>
</dd></dl>

<p>No module is imported during the “Core” phase and the <code class="docutils literal notranslate"><span class="pre">importlib</span></code> module is
not configured: the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Path Configuration</span></a> is only
applied during the “Main” phase. It may allow to customize Python in Python to
override or tune the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Path Configuration</span></a>, maybe
install a custom <a class="reference internal" href="../library/sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a> importer or an import hook, etc.</p>
<p>It may become possible to calculate the <a class="reference internal" href="#init-path-config"><span class="std std-ref">Path Configuration</span></a> in Python, after the Core phase and before the Main phase,
which is one of the <span class="target" id="index-38"></span><a class="pep reference external" href="https://peps.python.org/pep-0432/"><strong>PEP 432</strong></a> motivation.</p>
<p>The “Core” phase is not properly defined: what should be and what should
not be available at this phase is not specified yet. The API is marked
as private and provisional: the API can be modified or even be removed
anytime until a proper public API is designed.</p>
<p>Example running Python code between “Core” and “Main” initialization
phases:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">init_python</span><span class="p">(</span><span class="kt">void</span><span class="p">)</span>
<span class="p">{</span>
<span class="w">    </span><span class="n">PyStatus</span><span class="w"> </span><span class="n">status</span><span class="p">;</span>

<span class="w">    </span><span class="n">PyConfig</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="n">PyConfig_InitPythonConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="n">config</span><span class="p">.</span><span class="n">_init_main</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/* ... customize &#39;config&#39; configuration ... */</span>

<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_InitializeFromConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="n">PyConfig_Clear</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Py_ExitStatusException</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/* Use sys.stderr because sys.stdout is only created</span>
<span class="cm">       by _Py_InitializeMain() */</span>
<span class="w">    </span><span class="kt">int</span><span class="w"> </span><span class="n">res</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyRun_SimpleString</span><span class="p">(</span>
<span class="w">        </span><span class="s">&quot;import sys; &quot;</span>
<span class="w">        </span><span class="s">&quot;print(&#39;Run Python code before _Py_InitializeMain&#39;, &quot;</span>
<span class="w">               </span><span class="s">&quot;file=sys.stderr)&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">res</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">exit</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/* ... put more configuration code here ... */</span>

<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">_Py_InitializeMain</span><span class="p">();</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">PyStatus_Exception</span><span class="p">(</span><span class="n">status</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Py_ExitStatusException</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Python Initialization Configuration</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#pywidestringlist">PyWideStringList</a></li>
<li><a class="reference internal" href="#pystatus">PyStatus</a></li>
<li><a class="reference internal" href="#pypreconfig">PyPreConfig</a></li>
<li><a class="reference internal" href="#preinitialize-python-with-pypreconfig">Preinitialize Python with PyPreConfig</a></li>
<li><a class="reference internal" href="#pyconfig">PyConfig</a></li>
<li><a class="reference internal" href="#initialization-with-pyconfig">Initialization with PyConfig</a></li>
<li><a class="reference internal" href="#isolated-configuration">Isolated Configuration</a></li>
<li><a class="reference internal" href="#python-configuration">Python Configuration</a></li>
<li><a class="reference internal" href="#python-path-configuration">Python Path Configuration</a></li>
<li><a class="reference internal" href="#py-runmain">Py_RunMain()</a></li>
<li><a class="reference internal" href="#py-getargcargv">Py_GetArgcArgv()</a></li>
<li><a class="reference internal" href="#multi-phase-initialization-private-provisional-api">Multi-Phase Initialization Private Provisional API</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="init.html"
                          title="previous chapter">Initialization, Finalization, and Threads</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="memory.html"
                          title="next chapter">Memory Management</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/init_config.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="memory.html" title="Memory Management"
             >next</a> |</li>
        <li class="right" >
          <a href="init.html" title="Initialization, Finalization, and Threads"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Initialization Configuration</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>