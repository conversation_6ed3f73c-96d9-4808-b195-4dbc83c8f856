2025-10-02 11:13:48,090 9516 WARNING ? odoo.tools.config: missing --http-interface/http_interface, using 0.0.0.0 by default, will change to 127.0.0.1 in 20.0 
2025-10-02 11:13:48,090 9516 INFO ? odoo: Odoo version 19.0-20251002 
2025-10-02 11:13:48,090 9516 INFO ? odoo: Using configuration file at d:\odoo_19.0\server\odoo.conf 
2025-10-02 11:13:48,090 9516 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0\\server\\odoo\\addons', 'C:\\Users\\<USER>\\AppData\\Local\\OpenERP S.A.\\Odoo\\addons\\19.0', 'd:\\odoo_19.0\\server\\odoo\\addons', 'd:\\odoo_19.0\\server\\addons']) 
2025-10-02 11:13:48,090 9516 INFO ? odoo: database: openpg@localhost:5432 
2025-10-02 11:13:55,175 9516 INFO ? odoo.service.server: Initiating shutdown 
2025-10-02 11:13:55,175 9516 INFO ? odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-10-02 11:13:57,126 13392 INFO ? odoo: Odoo version 19.0-20251002 
2025-10-02 11:13:57,126 13392 INFO ? odoo: Using configuration file at d:\odoo_19.0\server\odoo.conf 
2025-10-02 11:13:57,126 13392 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0\\server\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\19.0', 'd:\\odoo_19.0\\server\\odoo\\addons', 'd:\\odoo_19.0\\server\\addons']) 
2025-10-02 11:13:57,126 13392 INFO ? odoo: database: openpg@localhost:5432 
2025-10-02 11:13:57,882 13392 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-02 11:13:57,982 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:13:58,045 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:12,008 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:12,029 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:12] "GET / HTTP/1.1" 303 - 0 0.000 0.077
2025-10-02 11:14:12,213 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:12,250 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:12,266 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:12] "GET /odoo HTTP/1.1" 303 - 0 0.000 0.233
2025-10-02 11:14:12,317 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:13,165 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:13,216 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:13] "GET /web/database/selector HTTP/1.1" 200 - 0 0.000 0.945
2025-10-02 11:14:13,584 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:13,608 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:13] "GET /web/static/src/libs/fontawesome/css/font-awesome.css HTTP/1.1" 200 - 0 0.000 0.074
2025-10-02 11:14:13,783 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:13,783 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:13] "GET /web/static/lib/bootstrap/dist/css/bootstrap.css HTTP/1.1" 200 - 0 0.000 0.249
2025-10-02 11:14:13,850 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:13,850 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:13] "GET /web/static/lib/bootstrap/js/dist/util/index.js HTTP/1.1" 200 - 0 0.000 0.314
2025-10-02 11:14:13,898 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:13,898 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:13] "GET /web/static/lib/bootstrap/js/dist/dom/data.js HTTP/1.1" 200 - 0 0.000 0.360
2025-10-02 11:14:13,965 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:13,965 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:13] "GET /web/static/lib/bootstrap/js/dist/dom/event-handler.js HTTP/1.1" 200 - 0 0.000 0.425
2025-10-02 11:14:14,028 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,030 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/dom/manipulator.js HTTP/1.1" 200 - 0 0.000 0.489
2025-10-02 11:14:14,085 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,086 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/dom/selector-engine.js HTTP/1.1" 200 - 0 0.000 0.169
2025-10-02 11:14:14,165 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,165 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/util/config.js HTTP/1.1" 200 - 0 0.000 0.050
2025-10-02 11:14:14,224 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,225 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/util/component-functions.js HTTP/1.1" 200 - 0 0.000 0.061
2025-10-02 11:14:14,383 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,383 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/util/backdrop.js HTTP/1.1" 200 - 0 0.000 0.156
2025-10-02 11:14:14,450 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,450 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/util/focustrap.js HTTP/1.1" 200 - 0 0.000 0.168
2025-10-02 11:14:14,498 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,498 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/util/scrollbar.js HTTP/1.1" 200 - 0 0.000 0.150
2025-10-02 11:14:14,565 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,565 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/base-component.js HTTP/1.1" 200 - 0 0.000 0.182
2025-10-02 11:14:14,621 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,623 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/lib/bootstrap/js/dist/modal.js HTTP/1.1" 200 - 0 0.000 0.142
2025-10-02 11:14:14,682 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:14,684 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:14] "GET /web/static/src/public/database_manager.js HTTP/1.1" 200 - 0 0.000 0.151
2025-10-02 11:14:15,054 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:15,056 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:15] "GET /web/static/img/logo2.png HTTP/1.1" 200 - 0 0.000 0.177
2025-10-02 11:14:15,329 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:15,330 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:15] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 200 - 0 0.000 0.065
2025-10-02 11:14:15,733 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:14:15,733 13392 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:14:15] "GET /web/static/img/favicon.ico HTTP/1.1" 200 - 0 0.000 0.067
2025-10-02 11:15:45,138 13392 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:15:48,151 13392 INFO None odoo.service.db: Create database `Learn_odoo`. 
2025-10-02 11:15:48,201 13392 INFO None odoo.sql_db: Connection to the database failed 
2025-10-02 11:15:48,201 13392 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "D:\odoo_19.0\server\odoo\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
  File "D:\odoo_19.0\server\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\service\db.py", line 519, in dispatch
    return g[exp_method_name](*params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\service\db.py", line 52, in if_db_mgt_enabled
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\service\db.py", line 180, in exp_create_database
    _create_empty_database(db_name)
  File "D:\odoo_19.0\server\odoo\service\db.py", line 130, in _create_empty_database
    with closing(db.cursor()) as cr:
                 ^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\sql_db.py", line 756, in cursor
    return Cursor(self.__pool, self.__dbname, self.__dsn)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\sql_db.py", line 358, in __init__
    self._cnx: PsycoConnection = pool.borrow(dsn)
                                 ^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\tools\func.py", line 88, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\sql_db.py", line 680, in borrow
    result = psycopg2.connect(
             ^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\python\Lib\site-packages\psycopg2\__init__.py", line 122, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.OperationalError: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "openpg"

2025-10-02 11:15:49,006 13392 INFO None odoo.sql_db: Connection to the database failed 
2025-10-02 11:15:49,031 13392 INFO None werkzeug: 127.0.0.1 - - [02/Oct/2025 11:15:49] "POST /web/database/create HTTP/1.1" 200 - 0 0.000 4.065
2025-10-02 11:17:57,893 13392 WARNING ? odoo.service.server: Thread <Thread(odoo.service.http.request.16916, started 16916)> virtual real time limit (132/120s) reached. 
2025-10-02 11:17:57,893 13392 WARNING ? odoo.service.server: Thread <Thread(odoo.service.http.request.8472, started 8472)> virtual real time limit (132/120s) reached. 
2025-10-02 11:17:57,893 13392 INFO ? odoo.service.server: Dumping stacktrace of limit exceeding threads before reloading 
2025-10-02 11:17:57,897 13392 INFO ? odoo.tools.misc: 
# Thread: <Thread(odoo.service.http.request.8472, started 8472)> (db:n/a) (uid:n/a) (url:n/a) (qc:n/a qt:n/a pt:n/a)
File: "D:\odoo_19.0\python\Lib\threading.py", line 1030, in _bootstrap
  self._bootstrap_inner()
File: "D:\odoo_19.0\python\Lib\threading.py", line 1073, in _bootstrap_inner
  self.run()
File: "D:\odoo_19.0\python\Lib\threading.py", line 1010, in run
  self._target(*self._args, **self._kwargs)
File: "D:\odoo_19.0\python\Lib\socketserver.py", line 692, in process_request_thread
  self.finish_request(request, client_address)
File: "D:\odoo_19.0\python\Lib\socketserver.py", line 362, in finish_request
  self.RequestHandlerClass(request, client_address, self)
File: "D:\odoo_19.0\python\Lib\socketserver.py", line 761, in __init__
  self.handle()
File: "D:\odoo_19.0\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
  super().handle()
File: "D:\odoo_19.0\python\Lib\http\server.py", line 436, in handle
  self.handle_one_request()
File: "D:\odoo_19.0\python\Lib\http\server.py", line 404, in handle_one_request
  self.raw_requestline = self.rfile.readline(65537)
File: "D:\odoo_19.0\python\Lib\socket.py", line 707, in readinto
  return self._sock.recv_into(b)

# Thread: <Thread(odoo.service.http.request.16916, started 16916)> (db:n/a) (uid:n/a) (url:n/a) (qc:n/a qt:n/a pt:n/a)
File: "D:\odoo_19.0\python\Lib\threading.py", line 1030, in _bootstrap
  self._bootstrap_inner()
File: "D:\odoo_19.0\python\Lib\threading.py", line 1073, in _bootstrap_inner
  self.run()
File: "D:\odoo_19.0\python\Lib\threading.py", line 1010, in run
  self._target(*self._args, **self._kwargs)
File: "D:\odoo_19.0\python\Lib\socketserver.py", line 692, in process_request_thread
  self.finish_request(request, client_address)
File: "D:\odoo_19.0\python\Lib\socketserver.py", line 362, in finish_request
  self.RequestHandlerClass(request, client_address, self)
File: "D:\odoo_19.0\python\Lib\socketserver.py", line 761, in __init__
  self.handle()
File: "D:\odoo_19.0\python\Lib\site-packages\werkzeug\serving.py", line 390, in handle
  super().handle()
File: "D:\odoo_19.0\python\Lib\http\server.py", line 436, in handle
  self.handle_one_request()
File: "D:\odoo_19.0\python\Lib\http\server.py", line 404, in handle_one_request
  self.raw_requestline = self.rfile.readline(65537)
File: "D:\odoo_19.0\python\Lib\socket.py", line 707, in readinto
  return self._sock.recv_into(b) 
2025-10-02 11:18:01,182 8980 INFO ? odoo: Odoo version 19.0-20251002 
2025-10-02 11:18:01,182 8980 INFO ? odoo: Using configuration file at d:\odoo_19.0\server\odoo.conf 
2025-10-02 11:18:01,182 8980 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0\\server\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\19.0', 'd:\\odoo_19.0\\server\\odoo\\addons', 'd:\\odoo_19.0\\server\\addons']) 
2025-10-02 11:18:01,182 8980 INFO ? odoo: database: openpg@localhost:5432 
2025-10-02 11:18:02,058 8980 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-10-02 11:18:02,127 8980 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:18:02,199 8980 INFO ? odoo.sql_db: Connection to the database failed 
2025-10-02 11:44:00,789 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:44:00] "GET /web/database/create HTTP/1.1" 405 - 1 0.019 0.106
2025-10-02 11:44:00,937 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:44:00] "GET /favicon.ico HTTP/1.1" 404 - 1 0.000 0.073
2025-10-02 11:44:03,805 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:44:03] "GET /web/database/create HTTP/1.1" 405 - 1 0.000 0.086
2025-10-02 11:44:05,871 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:44:05] "GET /web/database/create HTTP/1.1" 405 - 1 0.016 0.057
2025-10-02 11:45:08,109 8980 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "D:\odoo_19.0\server\odoo\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
  File "D:\odoo_19.0\server\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\service\db.py", line 518, in dispatch
    check_super(passwd)
  File "D:\odoo_19.0\server\odoo\service\db.py", line 63, in check_super
    raise odoo.exceptions.AccessDenied()
odoo.exceptions.AccessDenied: Access Denied
2025-10-02 11:45:09,081 8980 INFO None werkzeug: 127.0.0.1 - - [02/Oct/2025 11:45:09] "POST /web/database/create HTTP/1.1" 200 - 2 0.020 2.692
2025-10-02 11:45:49,413 8980 ERROR None odoo.addons.web.controllers.database: Database creation error. 
Traceback (most recent call last):
  File "D:\odoo_19.0\server\odoo\addons\web\controllers\database.py", line 81, in create
    dispatch_rpc('db', 'create_database', [master_pwd, name, bool(post.get('demo')), lang, password, post['login'], country_code, post['phone']])
  File "D:\odoo_19.0\server\odoo\http.py", line 439, in dispatch_rpc
    return dispatch(method, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\odoo_19.0\server\odoo\service\db.py", line 518, in dispatch
    check_super(passwd)
  File "D:\odoo_19.0\server\odoo\service\db.py", line 63, in check_super
    raise odoo.exceptions.AccessDenied()
odoo.exceptions.AccessDenied: Access Denied
2025-10-02 11:45:50,249 8980 INFO None werkzeug: 127.0.0.1 - - [02/Oct/2025 11:45:50] "POST /web/database/create HTTP/1.1" 200 - 2 0.014 2.483
2025-10-02 11:46:10,741 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:10] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 - 1 0.008 0.126
2025-10-02 11:46:11,142 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:11] "GET /web/static/lib/bootstrap/dist/css/bootstrap.css.map HTTP/1.1" 200 - 1 0.008 0.147
2025-10-02 11:46:11,845 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:11] "GET /web/static/lib/bootstrap/js/dist/util/index.js.map HTTP/1.1" 404 - 1 0.009 0.376
2025-10-02 11:46:11,845 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:11] "GET /web/static/lib/bootstrap/js/dist/dom/data.js.map HTTP/1.1" 404 - 1 0.000 0.369
2025-10-02 11:46:11,860 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:11] "GET /web/static/lib/bootstrap/js/dist/dom/event-handler.js.map HTTP/1.1" 404 - 1 0.000 0.369
2025-10-02 11:46:11,860 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:11] "GET /web/static/lib/bootstrap/js/dist/dom/manipulator.js.map HTTP/1.1" 404 - 1 0.016 0.338
2025-10-02 11:46:11,860 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:11] "GET /web/static/lib/bootstrap/js/dist/dom/selector-engine.js.map HTTP/1.1" 404 - 1 0.000 0.354
2025-10-02 11:46:11,860 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:11] "GET /web/static/lib/bootstrap/js/dist/util/config.js.map HTTP/1.1" 404 - 1 0.016 0.322
2025-10-02 11:46:12,774 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:12] "GET /web/static/lib/bootstrap/js/dist/util/backdrop.js.map HTTP/1.1" 404 - 1 0.016 0.586
2025-10-02 11:46:12,775 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:12] "GET /web/static/lib/bootstrap/js/dist/util/focustrap.js.map HTTP/1.1" 404 - 1 0.008 0.591
2025-10-02 11:46:12,776 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:12] "GET /web/static/lib/bootstrap/js/dist/util/component-functions.js.map HTTP/1.1" 404 - 1 0.011 0.598
2025-10-02 11:46:12,783 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:12] "GET /web/static/lib/bootstrap/js/dist/util/scrollbar.js.map HTTP/1.1" 404 - 1 0.016 0.585
2025-10-02 11:46:12,783 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:12] "GET /web/static/lib/bootstrap/js/dist/modal.js.map HTTP/1.1" 404 - 1 0.023 0.570
2025-10-02 11:46:12,783 8980 INFO ? werkzeug: 127.0.0.1 - - [02/Oct/2025 11:46:12] "GET /web/static/lib/bootstrap/js/dist/base-component.js.map HTTP/1.1" 404 - 1 0.016 0.577
2025-10-02 11:47:04,380 17304 INFO ? odoo: Odoo version 19.0-20251002 
2025-10-02 11:47:04,381 17304 INFO ? odoo: Using configuration file at d:\odoo_19.0\server\odoo.conf 
2025-10-02 11:47:04,381 17304 INFO ? odoo: addons paths: _NamespacePath(['D:\\odoo_19.0\\server\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\19.0', 'd:\\odoo_19.0\\server\\odoo\\addons', 'd:\\odoo_19.0\\server\\addons']) 
2025-10-02 11:47:04,381 17304 INFO ? odoo: database: openpg@localhost:5432 
2025-10-02 11:47:06,671 17304 WARNING ? py.warnings: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyPDF2\__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  File "D:\odoo_19.0\server\odoo-bin", line 6, in <module>
    odoo.cli.main()
  File "D:\odoo_19.0\server\odoo\cli\command.py", line 133, in main
    command().run(args)
  File "D:\odoo_19.0\server\odoo\cli\server.py", line 127, in run
    main(args)
  File "D:\odoo_19.0\server\odoo\cli\server.py", line 118, in main
    rc = server.start(preload=config['db_name'], stop=stop)
  File "D:\odoo_19.0\server\odoo\service\server.py", line 1545, in start
    load_server_wide_modules()
  File "D:\odoo_19.0\server\odoo\service\server.py", line 1461, in load_server_wide_modules
    load_openerp_module(m)
  File "D:\odoo_19.0\server\odoo\modules\module.py", line 496, in load_openerp_module
    __import__(qualname)
  File "D:\odoo_19.0\server\odoo\addons\base\__init__.py", line 4, in <module>
    from . import models
  File "D:\odoo_19.0\server\odoo\addons\base\models\__init__.py", line 12, in <module>
    from . import ir_actions_report
  File "D:\odoo_19.0\server\odoo\addons\base\models\ir_actions_report.py", line 32, in <module>
    from odoo.tools.pdf import PdfFileReader, PdfFileWriter, PdfReadError
  File "D:\odoo_19.0\server\odoo\tools\pdf\__init__.py", line 33, in <module>
    import PyPDF2.filters  # needed after PyPDF2 2.0.0 and before 2.11.0
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyPDF2\__init__.py", line 21, in <module>
    warnings.warn(
 
2025-10-02 11:47:08,984 17304 INFO your_database_name odoo.modules.loading: Initializing database your_database_name 
2025-10-02 11:47:11,187 17304 INFO your_database_name odoo.modules.loading: skipping reset_modules_state, ir_module_module table does not exists 
2025-10-02 11:47:11,188 17304 ERROR your_database_name odoo.registry: Failed to load registry 
2025-10-02 11:47:11,188 17304 CRITICAL your_database_name odoo.service.server: Failed to initialize database `your_database_name`. 
Traceback (most recent call last):
  File "D:\odoo_19.0\server\odoo\service\server.py", line 1509, in preload_registries
    registry = Registry.new(dbname, update_module=update_module, install_modules=config['init'], upgrade_modules=config['update'], reinit_modules=config['reinit'])
  File "D:\odoo_19.0\server\odoo\tools\func.py", line 88, in locked
    return func(inst, *args, **kwargs)
  File "D:\odoo_19.0\server\odoo\orm\registry.py", line 185, in new
    load_modules(
    ~~~~~~~~~~~~^
        registry,
        ^^^^^^^^^
    ...<4 lines>...
        new_db_demo=new_db_demo,
        ^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\odoo_19.0\server\odoo\modules\loading.py", line 361, in load_modules
    modules_db.initialize(cr)
    ~~~~~~~~~~~~~~~~~~~~~^^^^
  File "D:\odoo_19.0\server\odoo\modules\db.py", line 47, in initialize
    for info in odoo.modules.Manifest.all_addon_manifests():
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\odoo_19.0\server\odoo\modules\module.py", line 321, in all_addon_manifests
    for file_name in os.listdir(adp):
                     ~~~~~~~~~~^^^^^
PermissionError: [WinError 5] Access is denied: 'c:\\users\\<USER>\\appdata\\local\\openerp s.a\\odoo\\addons\\19.0'
2025-10-02 11:47:11,192 17304 INFO your_database_name odoo.service.server: Initiating shutdown 
2025-10-02 11:47:11,192 17304 INFO your_database_name odoo.service.server: Hit CTRL-C again or send a second signal to force the shutdown. 
2025-10-02 11:47:11,192 17304 INFO your_database_name odoo.sql_db: ConnectionPool(read/write;used=0/count=0/max=64): Closed 1 connections  
