[options]
addons_path = d:\odoo_19.0\server\odoo\addons
admin_passwd = $pbkdf2-sha512$600000$qNW69/4/B.AcI0TIGcMYow$1mZbCXxYcSxWrNsA9S9eu3XMBrSqkI1vd.2wB.NtDwI50vSob0zKizGoJDBIGWYVqRRULUnZAZ9kmfwN8yUwVQ
csv_internal_sep = ,
data_dir = C:\Users\<USER>\AppData\Local\OpenERP S.A.\Odoo
db_app_name = odoo-{pid}
db_host = localhost
db_maxconn = 64
db_maxconn_gevent = None
db_name = odoo_learn
db_password = openpgpwd
db_port = 5432
db_replica_host = None
db_replica_port = None
db_sslmode = prefer
db_template = template0
db_user = openpg
dbfilter = 
email_from = 
from_filter = 
geoip_city_db = /usr/share/GeoIP/GeoLite2-City.mmdb
geoip_country_db = /usr/share/GeoIP/GeoLite2-Country.mmdb
gevent_port = 8072
http_enable = True
http_interface = 0.0.0.0
http_port = 8069
limit_memory_soft = 2147483648
limit_time_real = 120
limit_time_real_cron = -1
limit_time_worker_cron = 0
list_db = True
log_db = 
log_db_level = warning
log_handler = :INFO
log_level = info
logfile = d:\odoo_19.0\server\odoo.log
max_cron_threads = 2
osv_memory_count_limit = 0
pg_path = 
pidfile = 
pre_upgrade_scripts = 
proxy_mode = False
reportgz = False
screencasts = 
screenshots = C:\Users\<USER>\AppData\Local\Temp\odoo_tests
server_wide_modules = base,rpc,web
smtp_password = 
smtp_port = 25
smtp_server = localhost
smtp_ssl = False
smtp_ssl_certificate_filename = 
smtp_ssl_private_key_filename = 
smtp_user = 
syslog = False
transient_age_limit = 1.0
unaccent = False
upgrade_path = 
websocket_keep_alive_timeout = 3600
websocket_rate_limit_burst = 10
websocket_rate_limit_delay = 0.2
with_demo = False
x_sendfile = False

